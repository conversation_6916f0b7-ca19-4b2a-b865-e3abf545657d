{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "Python: Attach (windows-x86_64/linux-x86_64)",
            "type": "python",
            "request": "attach",
            "port": 3000,
            "host": "localhost"
        },
        {
            "name": "Python: Train Environment",
            "type": "python",
            "request": "launch",
            "args" : ["--task", "Isaac-Reach-Franka-v0", "--headless"],
            "program": "${workspaceFolder}/source/standalone/workflows/rsl_rl/train.py",
            "console": "integratedTerminal"
        },
        {
            "name": "Python: Play Environment",
            "type": "python",
            "request": "launch",
            "args" : ["--task", "Isaac-Reach-Franka-v0", "--num_envs", "32"],
            "program": "${workspaceFolder}/source/standalone/workflows/rsl_rl/play.py",
            "console": "integratedTerminal"
        }
    ]
}
