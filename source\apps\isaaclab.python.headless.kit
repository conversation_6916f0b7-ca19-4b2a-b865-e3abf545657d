##
# Adapted from: _isaac_sim/apps/omni.isaac.sim.python.gym.headless.kit
##

[package]
title = "Isaac Lab Python Headless"
description = "An app for running Isaac Lab headlessly"
version = "1.4.0"

# That makes it browsable in UI with "experience" filter
keywords = ["experience", "app", "isaaclab", "python", "headless"]

[settings]
# Note: This path was adapted to be respective to the kit-exe file location
app.versionFile = "${exe-path}/VERSION"
app.folder = "${exe-path}/"
app.name = "Isaac-Sim"
app.version = "4.2.0"

##################################
# Omniverse related dependencies #
##################################
[dependencies]
"omni.physx" = {}
"omni.physx.tensors" = {}
"omni.physx.fabric" = {}
"omni.warp.core" = {}
"usdrt.scenegraph" = {}
"omni.kit.telemetry" = {}


[settings]
renderer.active = "rtx"

app.content.emptyStageOnStart = false

# Disable print outs on extension startup information
# this only disables the app print_and_log function
app.enableStdoutOutput = false

# Setting the port for the embedded http server
exts."omni.services.transport.server.http".port = 8211

# default viewport is fill
app.runLoops.rendering_0.fillResolution = false
exts."omni.kit.window.viewport".blockingGetViewportDrawable = false

# Fix PlayButtonGroup error
exts."omni.kit.widget.toolbar".PlayButton.enabled = false

# disable replicator orchestrator for better runtime perf
exts."omni.replicator.core".Orchestrator.enabled = false

[settings.app.settings]
persistent = true
dev_build = false
fabricDefaultStageFrameHistoryCount = 3 # needed for omni.syntheticdata TODO105 still true?

[settings.app.window]
title = "Isaac Sim"
hideUi = false
_iconSize = 256
iconPath = "${omni.isaac.app.setup}/data/omni.isaac.sim.png"

# width = 1700
# height = 900
# x = -1
# y = -1

# Fonts
[setting.app.font]
file = "${fonts}/OpenSans-SemiBold.ttf"
size = 16

# [setting.app.runLoops]
# main.rateLimitEnabled = false
# main.rateLimitFrequency = 60
# main.rateLimitUseBusyLoop = false
# rendering_0.rateLimitEnabled = false

[settings.exts.'omni.kit.window.extensions']
# List extensions here we want to show as featured when extension manager is opened
featuredExts = []

[settings.app.python]
# These disable the kit app from also printing out python output, which gets confusing
interceptSysStdOutput = false
logSysStdOutput = false

[settings]
# MGPU is always on, you can turn it from the settings, and force this off to save even more resource if you
# only want to use a single GPU on your MGPU system
# False for Isaac Sim
renderer.multiGpu.enabled = true
renderer.multiGpu.autoEnable = true
'rtx-transient'.resourcemanager.enableTextureStreaming = true
app.asyncRendering = false
app.asyncRenderingLowLatency = false
app.hydraEngine.waitIdle = false
# app.hydra.aperture.conform = 4 # in 105.1 pixels are square by default
omni.replicator.asyncRendering = false

# Enable Iray and pxr by setting this to "rtx,iray,pxr"
renderer.enabled = "rtx"

# Avoid warning on shutdown from audio context
app.audio.enabled = false

# Enable Vulkan - avoids torch+cu12 error on windows
app.vulkan = true

# hide NonToggleable Exts
exts."omni.kit.window.extensions".hideNonToggleableExts = true
exts."omni.kit.window.extensions".showFeatureOnly = false

# Hang Detector
################################
# app.hangDetector.enabled = false
# app.hangDetector.timeout = 120


# set the default ros bridge to disable on startup
isaac.startup.ros_bridge_extension = ""

# Extensions
###############################
[settings.exts."omni.kit.registry.nucleus"]
registries = [
    { name = "kit/default", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/shared" },
    { name = "kit/sdk", url = "https://ovextensionsprod.blob.core.windows.net/exts/kit/prod/sdk/${kit_version_short}/${kit_git_hash}" },
    { name = "kit/community", url = "https://dw290v42wisod.cloudfront.net/exts/kit/community" },
]

[settings.app.extensions]
skipPublishVerification = false
registryEnabled = true

[settings.exts."omni.kit.window.modifier.titlebar"]
titleFormatString = "  Isaac Sim  {version:${app}/../SHORT_VERSION,font_color=0x909090,font_size=16} {separator} {file, board=true}"
showFileFullPath = true
icon.file = "${app}/../exts/omni.isaac.app.setup/data/nvidia-omniverse-isaacsim.ico"
icon.size = 256
defaultFont.name = "Arial"
defaultFont.size = 16
defaultFont.color = 0xD0D0D0
separator.color = 0x00B976
separator.width = 1
windowBorder.color = 0x0F0F0F
windowBorder.width = 2
colors.caption = 0x0F0F0F
colors.client = 0x0F0F0F
respondOnMouseUp = true
changeWindowRegion = true

[settings.crashreporter.data]
experience = "Isaac Sim"

# Isaac Sim Settings
###############################
[settings.app.renderer]
skipWhileMinimized = false
sleepMsOnFocus = 0
sleepMsOutOfFocus = 0
resolution.width=1280
resolution.height=720

# default camera position in meters
[settings.app.viewport]
defaultCamPos.x = 5
defaultCamPos.y = 5
defaultCamPos.z = 5

[settings.rtx]
raytracing.fractionalCutoutOpacity = false
hydra.enableSemanticSchema = true
mdltranslator.mdlDistilling = false
# descriptorSets=60000
# reservedDescriptors=500000
# sceneDb.maxInstances=1000000
# Enable this for static scenes, improves visual quality
# directLighting.sampledLighting.enabled = true

[settings.persistent]
app.file.recentFiles = []
app.stage.upAxis = "Z"
app.stage.movePrimInPlace = false
app.stage.instanceableOnCreatingReference = false
app.stage.materialStrength = "weakerThanDescendants"

app.transform.gizmoUseSRT = true
app.viewport.grid.scale = 1.0
app.viewport.pickingMode = "kind:model.ALL"
app.viewport.camMoveVelocity = 0.05 # 5 m/s
app.viewport.gizmo.scale = 0.01 # scaled to meters
app.viewport.previewOnPeek = false
app.viewport.snapToSurface = false
app.viewport.displayOptions = 31951 # Disable Frame Rate and Resolution by default
app.window.uiStyle = "NvidiaDark"
app.primCreation.DefaultXformOpType = "Scale, Orient, Translate"
app.primCreation.DefaultXformOpOrder="xformOp:translate, xformOp:orient, xformOp:scale"
app.primCreation.typedDefaults.camera.clippingRange = [0.01, 10000000.0]
simulation.minFrameRate = 15
simulation.defaultMetersPerUnit = 1.0
omnigraph.updateToUsd = false
omnigraph.useSchemaPrims = true
omnigraph.disablePrimNodes = true
omni.replicator.captureOnPlay = true
omnihydra.useSceneGraphInstancing = true
renderer.startupMessageDisplayed = true # hides the IOMMU popup window

# Make Detail panel visible by default
app.omniverse.content_browser.options_menu.show_details = true
app.omniverse.filepicker.options_menu.show_details = true

[settings.physics]
updateToUsd = false
updateParticlesToUsd = false
updateVelocitiesToUsd = false
updateForceSensorsToUsd = false
outputVelocitiesLocalSpace = false
useFastCache = false
visualizationDisplayJoints = false
fabricUpdateTransformations = false
fabricUpdateVelocities = false
fabricUpdateForceSensors = false
fabricUpdateJointStates = false

# Performance improvement
resourcemonitor.timeBetweenQueries = 100

# Register extension folder from this repo in kit
[settings.app.exts]
folders = [
    "${exe-path}/exts",  # kit extensions
    "${exe-path}/extscore",  # kit core extensions
    "${exe-path}/../exts",  # isaac extensions
    "${exe-path}/../extscache",  # isaac cache extensions
    "${exe-path}/../extsPhysics",  # isaac physics extensions
    "${exe-path}/../isaacsim/exts",  # isaac extensions for pip
    "${exe-path}/../isaacsim/extscache",  # isaac cache extensions for pip
    "${exe-path}/../isaacsim/extsPhysics",  # isaac physics extensions for pip
    "${app}", # needed to find other app files
    "${app}/../extensions", # needed to find extensions in Isaac Lab
]

[settings.ngx]
enabled=true # Enable this for DLSS

########################
# Isaac Sim Extensions #
########################
[dependencies]
"omni.isaac.core" = {}
"omni.isaac.cloner" = {}
"omni.isaac.kit" = {}
"omni.kit.loop-isaac" = {}
