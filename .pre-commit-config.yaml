repos:
  - repo: https://github.com/python/black
    rev: 24.3.0
    hooks:
      - id: black
        args: ["--line-length", "120", "--unstable"]
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-simplify, flake8-return]
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: check-symlinks
      - id: destroyed-symlinks
      - id: check-added-large-files
        args: ["--maxkb=2000"]  # restrict files more than 2 MB. Should use git-lfs instead.
      - id: check-yaml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: check-toml
      - id: end-of-file-fixer
      - id: check-shebang-scripts-are-executable
      - id: detect-private-key
      - id: debug-statements
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: isort (python)
        args: ["--profile", "black", "--filter-files"]
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.15.1
    hooks:
      - id: pyupgrade
        args: ["--py310-plus"]
        # FIXME: This is a hack because Pytorch does not like: torch.Tensor | dict aliasing
        exclude: "source/extensions/omni.isaac.lab/omni/isaac/lab/envs/common.py|source/extensions/omni.isaac.lab/omni/isaac/lab/ui/widgets/image_plot.py"
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.6
    hooks:
      - id: codespell
        additional_dependencies:
        - tomli
  # FIXME: Figure out why this is getting stuck under VPN.
  # - repo: https://github.com/RobertCraigie/pyright-python
  #   rev: v1.1.315
  #   hooks:
  #   - id: pyright
  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.1
    hooks:
      - id: insert-license
        files: \.py$
        args:
          # - --remove-header    # Remove existing license headers. Useful when updating license.
          - --license-filepath
          - .github/LICENSE_HEADER.txt
          - --use-current-year
  - repo: https://github.com/pre-commit/pygrep-hooks
    rev: v1.10.0
    hooks:
      - id: rst-backticks
      - id: rst-directive-colons
      - id: rst-inline-touching-normal
