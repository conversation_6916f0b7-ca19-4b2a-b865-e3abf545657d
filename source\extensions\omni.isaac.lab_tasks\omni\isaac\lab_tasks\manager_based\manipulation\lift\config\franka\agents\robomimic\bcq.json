{"algo_name": "bcq", "experiment": {"name": "bcq", "validate": true, "logging": {"terminal_output_to_txt": true, "log_tb": true}, "save": {"enabled": true, "every_n_seconds": null, "every_n_epochs": 50, "epochs": [], "on_best_validation": true, "on_best_rollout_return": false, "on_best_rollout_success_rate": false}, "epoch_every_n_steps": 100, "validation_epoch_every_n_steps": 10, "env": null, "additional_envs": null, "render": false, "render_video": true, "keep_all_videos": false, "video_skip": 5, "rollout": {"enabled": false, "n": 50, "horizon": 400, "rate": 50, "warmstart": 0, "terminate_on_success": true}}, "train": {"data": null, "output_dir": "../bcq_trained_models", "num_data_workers": 0, "hdf5_cache_mode": "all", "hdf5_use_swmr": true, "hdf5_normalize_obs": false, "hdf5_filter_key": null, "seq_length": 1, "dataset_keys": ["actions", "rewards", "dones"], "goal_mode": null, "cuda": true, "batch_size": 100, "num_epochs": 200, "seed": 1}, "algo": {"optim_params": {"critic": {"learning_rate": {"initial": 0.001, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}, "start_epoch": -1, "end_epoch": -1}, "action_sampler": {"learning_rate": {"initial": 0.001, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}, "start_epoch": -1, "end_epoch": -1}, "actor": {"learning_rate": {"initial": 0.001, "decay_factor": 0.1, "epoch_schedule": []}, "regularization": {"L2": 0.0}, "start_epoch": -1, "end_epoch": -1}}, "discount": 0.99, "n_step": 1, "target_tau": 0.005, "infinite_horizon": false, "critic": {"use_huber": false, "max_gradient_norm": null, "value_bounds": null, "num_action_samples": 10, "num_action_samples_rollout": 100, "ensemble": {"n": 2, "weight": 0.75}, "distributional": {"enabled": false, "num_atoms": 51}, "layer_dims": [300, 400]}, "action_sampler": {"actor_layer_dims": [1024, 1024], "gmm": {"enabled": false, "num_modes": 5, "min_std": 0.0001, "std_activation": "softplus", "low_noise_eval": true}, "vae": {"enabled": true, "latent_dim": 14, "latent_clip": null, "kl_weight": 1.0, "decoder": {"is_conditioned": true, "reconstruction_sum_across_elements": false}, "prior": {"learn": false, "is_conditioned": false, "use_gmm": false, "gmm_num_modes": 10, "gmm_learn_weights": false, "use_categorical": false, "categorical_dim": 10, "categorical_gumbel_softmax_hard": false, "categorical_init_temp": 1.0, "categorical_temp_anneal_step": 0.001, "categorical_min_temp": 0.3}, "encoder_layer_dims": [300, 400], "decoder_layer_dims": [300, 400], "prior_layer_dims": [300, 400]}, "freeze_encoder_epoch": -1}, "actor": {"enabled": false, "perturbation_scale": 0.05, "layer_dims": [300, 400]}}, "observation": {"modalities": {"obs": {"low_dim": ["tool_dof_pos_scaled", "tool_positions", "object_relative_tool_positions", "object_desired_positions"], "rgb": [], "depth": [], "scan": []}, "goal": {"low_dim": [], "rgb": [], "depth": [], "scan": []}}, "encoder": {"low_dim": {"core_class": null, "core_kwargs": {}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {}}, "rgb": {"core_class": "VisualCore", "core_kwargs": {"feature_dimension": 64, "flatten": true, "backbone_class": "ResNet18Conv", "backbone_kwargs": {"pretrained": false, "input_coord_conv": false}, "pool_class": "SpatialSoftmax", "pool_kwargs": {"num_kp": 32, "learnable_temperature": false, "temperature": 1.0, "noise_std": 0.0, "output_variance": false}}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {"crop_height": 76, "crop_width": 76, "num_crops": 1, "pos_enc": false}}, "depth": {"core_class": "VisualCore", "core_kwargs": {"feature_dimension": 64, "flatten": true, "backbone_class": "ResNet18Conv", "backbone_kwargs": {"pretrained": false, "input_coord_conv": false}, "pool_class": "SpatialSoftmax", "pool_kwargs": {"num_kp": 32, "learnable_temperature": false, "temperature": 1.0, "noise_std": 0.0, "output_variance": false}}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {"crop_height": 76, "crop_width": 76, "num_crops": 1, "pos_enc": false}}, "scan": {"core_class": "ScanCore", "core_kwargs": {"feature_dimension": 64, "flatten": true, "pool_class": "SpatialSoftmax", "pool_kwargs": {"num_kp": 32, "learnable_temperature": false, "temperature": 1.0, "noise_std": 0.0, "output_variance": false}, "conv_activation": "relu", "conv_kwargs": {"out_channels": [32, 64, 64], "kernel_size": [8, 4, 2], "stride": [4, 2, 1]}}, "obs_randomizer_class": null, "obs_randomizer_kwargs": {"crop_height": 76, "crop_width": 76, "num_crops": 1, "pos_enc": false}}}}}