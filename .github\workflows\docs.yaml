name: Build & deploy docs

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  check-secrets:
    name: Check secrets
    runs-on: ubuntu-latest
    outputs:
      trigger-deploy: ${{ steps.trigger-deploy.outputs.defined }}
    steps:
    - id: trigger-deploy
      env:
        REPO_NAME: ${{ secrets.REPO_NAME }}
        BRANCH_REF: ${{ secrets.BRANCH_REF }}
      if: "${{ github.repository == env.REPO_NAME && github.ref == env.BRANCH_REF }}"
      run: echo "defined=true" >> "$GITHUB_OUTPUT"

  build-docs:
    name: Build Docs
    runs-on: ubuntu-latest
    needs: [check-secrets]

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Setup python
      uses: actions/setup-python@v2
      with:
        python-version: "3.10"
        architecture: x64

    - name: Install dev requirements
      working-directory: ./docs
      run: pip install -r requirements.txt

    - name: Check branch docs building
      working-directory: ./docs
      if: needs.check-secrets.outputs.trigger-deploy != 'true'
      run: make current-docs

    - name: Generate multi-version docs
      working-directory: ./docs
      run: |
        git fetch --prune --unshallow --tags
        make multi-docs

    - name: Upload docs artifact
      uses: actions/upload-artifact@v4
      with:
        name: docs-html
        path: ./docs/_build

  deploy-docs:
    name: Deploy Docs
    runs-on: ubuntu-latest
    needs: [check-secrets, build-docs]
    if: needs.check-secrets.outputs.trigger-deploy == 'true'

    steps:
    - name: Download docs artifact
      uses: actions/download-artifact@v4
      with:
        name: docs-html
        path: ./docs/_build

    - name: Deploy to gh-pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/_build
