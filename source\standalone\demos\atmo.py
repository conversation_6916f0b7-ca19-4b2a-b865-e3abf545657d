# pyright: reportGeneralTypeIssues=false

# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
ATMO (Aerial Tilting Multirotor Omnicopter) 模拟演示脚本

此脚本演示如何使用学习策略和模型预测控制(MPC)来模拟ATMO多旋翼飞行器。
ATMO是一种具有可倾斜旋翼的多旋翼飞行器，能够进行精确着陆和操控。

支持的控制算法：
- RL: 强化学习策略
- MPC: 模型预测控制
- MPC-PHI: 包含倾斜角度的MPC
- RL-BRTC: 结合BRTC控制器的强化学习
- MPC-BRTC: 结合BRTC控制器的MPC
- BRTC: 基础BRTC控制器

.. code-block:: bash

    # 使用方法
    ./isaaclab.sh -p source/standalone/demos/atmo.py

"""

# =============================================================================
# 库导入部分
# =============================================================================

# 基础Python库
import os                    # 操作系统接口，用于文件路径操作
import argparse              # 命令行参数解析
import torch                 # PyTorch深度学习框架
import numpy as np           # 数值计算库
from IPython import embed   # 交互式调试工具
import datetime              # 日期时间处理
import scipy                 # 科学计算库
import onnxruntime as ort    # ONNX运行时，用于加载训练好的神经网络模型
import matplotlib.pyplot as plt  # 绘图库
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed  # 并行计算
import time                  # 时间相关功能
import h5py                  # HDF5文件格式支持
import yaml                  # YAML配置文件解析

# 导入MPC acados实现
from acados_template          import AcadosOcpSolver  # Acados最优控制问题求解器
from atmo_mpc                 import create_ocp_solver_description, create_ocp_solver_description_phi  # ATMO MPC求解器描述
from atmo_parameters          import params_, get_cost_weights  # ATMO参数配置和代价权重
from atmo_brtc                import BRTC  # BRTC控制器实现

# 启动Isaac Sim模拟器
from omni.isaac.lab.app import AppLauncher

# 命令行参数解析和应用启动器设置
parser = argparse.ArgumentParser(description="演示如何使用学习策略模拟ATMO的脚本")
AppLauncher.add_app_launcher_args(parser)  # 添加Isaac Lab标准命令行参数
args_cli = parser.parse_args()             # 解析命令行参数
app_launcher = AppLauncher(args_cli)       # 创建应用启动器
simulation_app = app_launcher.app          # 获取仿真应用实例

# 导入Isaac Lab相关模块
import omni.isaac.lab.sim as sim_utils                                    # 仿真工具
from omni.isaac.lab.assets import Articulation                            # 关节机器人资产
from omni.isaac.lab.sim import SimulationContext                          # 仿真上下文
from omni.isaac.lab.scene import InteractiveSceneCfg, InteractiveScene    # 交互式场景配置和实现
from omni.isaac.lab_assets import ATMO_CFG  # isort:skip                  # ATMO机器人配置
from omni.isaac.lab.utils.math import euler_xyz_from_quat, quat_from_euler_xyz, matrix_from_quat  # 数学工具函数
from omni.isaac.lab.markers import VisualizationMarkers, VisualizationMarkersCfg  # 可视化标记器
from omni.isaac.lab.utils.assets import ISAAC_NUCLEUS_DIR, ISAACLAB_NUCLEUS_DIR   # 资产目录路径
from omni.isaac.lab.utils import configclass                              # 配置类装饰器
from omni.isaac.lab.assets import ArticulationCfg, AssetBaseCfg           # 关节配置和基础资产配置

# =============================================================================
# 场景配置类
# =============================================================================

@configclass
class ATMOSceneCfg(InteractiveSceneCfg):
    """ATMO场景配置类
    
    定义场景中的所有资产和环境设置，包括地面、光照和机器人配置。
    继承自InteractiveSceneCfg，提供交互式场景的基础功能。
    """

    # 地面平面配置 - 创建默认的地面平面作为着陆表面
    ground = AssetBaseCfg(prim_path="/World/defaultGroundPlane", spawn=sim_utils.GroundPlaneCfg())

    # 光照设置 - 配置环境光照，强度3000.0，颜色为灰白色
    distant_light = AssetBaseCfg(
        prim_path="/World/Light", spawn=sim_utils.DistantLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75))
    )

    # 机器人关节配置 - 使用ATMO配置并替换路径以支持多环境
    robot: ArticulationCfg = ATMO_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

# =============================================================================
# 可视化相关函数
# =============================================================================

def define_markers() -> VisualizationMarkers:
    """定义各种不同形状的可视化标记器
    
    创建用于可视化的标记器配置，包括多种几何形状和颜色。
    这些标记器可用于显示机器人位置、目标位置、轨迹等信息。
    
    返回:
        VisualizationMarkers: 配置好的可视化标记器对象
    """
    marker_cfg = VisualizationMarkersCfg(
        prim_path="/Visuals/myMarkers",
        markers={
            # 坐标框架标记 - 显示坐标系方向
            "frame": sim_utils.UsdFileCfg(
                usd_path=f"{ISAAC_NUCLEUS_DIR}/Props/UIElements/frame_prim.usd",
                scale=(0.5, 0.5, 0.5),
            ),
            # X轴箭头标记 - 青色，用于指示方向
            "arrow_x": sim_utils.UsdFileCfg(
                usd_path=f"{ISAAC_NUCLEUS_DIR}/Props/UIElements/arrow_x.usd",
                scale=(1.0, 0.5, 0.5),
                visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 1.0, 1.0)),
            ),
            # 小立方体标记 - 红色，用于标记特定位置
            "cube": sim_utils.CuboidCfg(
                size=(0.1, 0.1, 0.1),
                visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1.0, 0.0, 0.0)),
            ),
            # 球体标记 - 绿色，用于目标位置标记
            "sphere": sim_utils.SphereCfg(
                radius=0.5,
                visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 1.0, 0.0)),
            ),
            # 圆柱体标记 - 蓝色，用于路径点标记
            "cylinder": sim_utils.CylinderCfg(
                radius=0.5,
                height=1.0,
                visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0)),
            ),
            # 圆锥体标记 - 黄色，用于警告或特殊位置标记
            "cone": sim_utils.ConeCfg(
                radius=0.5,
                height=1.0,
                visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1.0, 1.0, 0.0)),
            ),
            # 网格模型标记 - 使用立方体网格，默认材质
            "mesh": sim_utils.UsdFileCfg(
                usd_path=f"{ISAAC_NUCLEUS_DIR}/Props/Blocks/DexCube/dex_cube_instanceable.usd",
                scale=(10.0, 10.0, 10.0),
            ),
            # 重新着色的网格模型标记 - 橙色立方体
            "mesh_recolored": sim_utils.UsdFileCfg(
                usd_path=f"{ISAAC_NUCLEUS_DIR}/Props/Blocks/DexCube/dex_cube_instanceable.usd",
                scale=(10.0, 10.0, 10.0),
                visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1.0, 0.25, 0.0)),
            ),
            # 机器人网格模型标记 - 使用ANYmal-C模型，绿色玻璃材质
            "robot_mesh": sim_utils.UsdFileCfg(
                usd_path=f"{ISAACLAB_NUCLEUS_DIR}/Robots/ANYbotics/ANYmal-C/anymal_c.usd",
                scale=(2.0, 2.0, 2.0),
                visual_material=sim_utils.GlassMdlCfg(glass_color=(0.0, 0.1, 0.0)),
            ),
        },
    )
    return VisualizationMarkers(marker_cfg)
 
def visualize_markers(robot, desired_position, visualizer):
    """可视化标记器函数 - 显示机器人位置、目标位置和旋翼位置
    
    在3D场景中显示重要的位置信息，帮助用户理解机器人的状态和目标。
    
    参数:
        robot: 机器人关节对象，包含机器人的状态数据
        desired_position: 目标位置张量 [x, y, z]
        visualizer: 可视化标记器对象，用于渲染标记
    """
    # 获取旋翼0的位置索引和当前位置
    rotor0 = robot.find_bodies("rotor0")[0][0]
    rotor0_pos = robot.data.body_pos_w[:, rotor0]
    
    # 堆叠标记器位置：机器人根链接位置、目标位置、旋翼0位置
    marker_locations = torch.stack([
                                    robot.data.root_link_pos_w, 
                                    desired_position.unsqueeze(0),
                                    rotor0_pos
                                    ], 
                                dim=1
                                ).squeeze()
    
    # 堆叠标记器方向：机器人根链接四元数、默认方向、默认方向
    marker_orientations = torch.stack([
                                        robot.data.root_link_quat_w, 
                                        torch.tensor([1.0, 0.0, 0.0, 0.0], device=args_cli.device).unsqueeze(0), 
                                        torch.tensor([1.0, 0.0, 0.0, 0.0], device=args_cli.device).unsqueeze(0)
                                        ]
                                    ).squeeze()

    # 可视化标记器 - 使用指定的标记器索引[框架, 框架, 立方体]
    visualizer.visualize(marker_locations, marker_orientations, marker_indices=[0,0,2])

def plot_data(actions_log, filtered_actions_log, pos_log, euler_log, lin_vel_log, ang_vel_log, tilt_angle_log, times_log, x_ref_log):
    """绘制仿真数据函数 - 生成动作、位置、速度等数据的时间序列图
    
    创建包含4个子图的图形，显示仿真过程中的关键数据变化趋势。
    
    参数:
        actions_log: 动作记录列表，包含原始控制输入
        filtered_actions_log: 滤波后动作记录列表
        pos_log: 位置记录列表 [x, y, z]
        euler_log: 欧拉角记录列表 [roll, pitch, yaw]
        lin_vel_log: 线速度记录列表 [vx, vy, vz]
        ang_vel_log: 角速度记录列表 [wx, wy, wz]
        tilt_angle_log: 倾斜角度记录列表
        times_log: 时间记录列表
        x_ref_log: 参考位置记录列表
    """
    # 创建4个子图的图形，设置图形大小
    fig, axs = plt.subplots(4, 1, figsize=(12, 12))
    fig.suptitle("ATMO仿真数据")

    # 绘制原始动作数据 - 包含4个推力器和倾斜角度
    actions = np.array(actions_log).squeeze()
    axs[0].plot(times_log, actions[:, 0, 0], label="推力器0")
    axs[0].plot(times_log, actions[:, 0,  1], label="推力器1")
    axs[0].plot(times_log, actions[:, 0,  2], label="推力器2")
    axs[0].plot(times_log, actions[:, 0,  3], label="推力器3")
    axs[0].plot(times_log, actions[:, 0,  4], label="倾斜角")
    axs[0].set_title("原始动作")
    axs[0].set_xlabel("时间 (秒)")
    axs[0].set_ylabel("数值")
    axs[0].legend()

    # 绘制滤波后的动作数据 - 经过低通滤波器处理的控制信号
    filtered_actions = np.array(filtered_actions_log).squeeze()
    axs[1].plot(times_log, filtered_actions[:, 0,  0], label="滤波推力器0")
    axs[1].plot(times_log, filtered_actions[:, 0,  1], label="滤波推力器1")
    axs[1].plot(times_log, filtered_actions[:, 0,  2], label="滤波推力器2")
    axs[1].plot(times_log, filtered_actions[:, 0,  3], label="滤波推力器3")
    axs[1].plot(times_log, filtered_actions[:, 0,  4], label="滤波倾斜角")
    axs[1].set_title("滤波后动作")
    axs[1].set_xlabel("时间 (秒)")
    axs[1].set_ylabel("数值")
    axs[1].legend()

    # # Plot the angular rates
    # angular_rates = np.array(observations_log).squeeze()[:, 15:18]
    # axs[2].plot(times_log, angular_rates[:, 0], label="Angular Rate X")
    # axs[2].plot(times_log, angular_rates[:, 1], label="Angular Rate Y")
    # axs[2].plot(times_log, angular_rates[:, 2], label="Angular Rate Z")
    # axs[2].set_title("Observations")
    # axs[2].set_xlabel("Time")
    # axs[2].set_ylabel("Value")
    # axs[2].legend()


    # Plot the observations
    pos = np.array(pos_log).squeeze()
    axs[2].plot(times_log, pos[:, 0,  0], label="X")
    axs[2].plot(times_log, pos[:, 0,  1], label="Y")
    axs[2].plot(times_log, pos[:, 0,  2], label="Z")
    axs[2].set_title("Observations")
    axs[2].set_xlabel("Time")
    axs[2].set_ylabel("Value")
    axs[2].legend()

    # plot the reference positions on axs[2]
    x_ref = np.array(x_ref_log).squeeze()
    axs[2].plot(times_log, x_ref[:, 0,  0], label="X Ref", linestyle="--")
    axs[2].plot(times_log, -x_ref[:, 0,  1], label="Y Ref", linestyle="--")
    axs[2].plot(times_log, -x_ref[:, 0,  2], label="Z Ref", linestyle="--")

    lin_vel = np.array(lin_vel_log).squeeze()
    axs[3].plot(times_log, lin_vel[:, 0,  0], label="VX")
    axs[3].plot(times_log, lin_vel[:, 0,  1], label="VY")
    axs[3].plot(times_log, lin_vel[:, 0,  2], label="VZ")
    axs[3].set_title("Observations")
    axs[3].set_xlabel("Time")
    axs[3].set_ylabel("Value")
    axs[3].legend()

    # also plot tilt angle
    tilt_angle = np.array(tilt_angle_log).squeeze()
    fig, ax = plt.subplots()
    ax.plot(times_log, tilt_angle)
    ax.set_title("Tilt Angle")
    ax.set_xlabel("Time")
    ax.set_ylabel("Tilt Angle")

def low_pass_filter(x, y, alpha):
    """低通滤波器函数

    实现一阶低通滤波器，用于平滑控制信号，减少高频噪声。

    参数:
        x: 输入信号（当前值）
        y: 输出信号（上一次滤波后的值）
        alpha: 滤波系数，范围[0,1]，值越大响应越快

    返回:
        滤波后的信号值
    """
    return alpha * x + (1 - alpha) * y

def get_state(robot):
    """获取机器人状态信息

    从机器人对象中提取完整的状态信息，包括位置、姿态、速度等。

    参数:
        robot: 机器人关节对象

    返回:
        tuple: (位置, 欧拉角, 四元数, 线速度, 角速度, 倾斜角度)
    """
    # 获取倾斜角度 - 从左臂关节获取倾斜角度
    joint0 = robot.find_joints("base_to_arml")[0]
    tilt_angle = robot.data.joint_pos[:, joint0[0]]

    # 获取位置 - 机器人根链接在世界坐标系中的位置
    pos = robot.data.root_link_pos_w

    # 获取姿态 - 四元数表示的方向
    quat = robot.data.root_link_quat_w
    roll, pitch, yaw = euler_xyz_from_quat(quat)
    # 将角度限制在[-π, π]范围内
    roll = torch.fmod(roll + np.pi, 2 * np.pi) - np.pi
    pitch = torch.fmod(pitch + np.pi, 2 * np.pi) - np.pi
    yaw = torch.fmod(yaw + np.pi, 2 * np.pi) - np.pi
    euler = torch.stack([roll, pitch, yaw], dim=-1)

    # 获取线速度 - 机器人质心在世界坐标系中的线速度
    lin_vel = robot.data.root_com_lin_vel_w

    # 获取角速度 - 机器人在体坐标系中的角速度
    ang_vel = robot.data.root_com_ang_vel_b

    return pos, euler, quat, lin_vel, ang_vel, tilt_angle

def get_observations(robot, desired_pos_w, action_history):
    """获取强化学习观测值

    构建用于强化学习策略的观测向量，包含相对位置、姿态、速度等信息。

    参数:
        robot: 机器人关节对象
        desired_pos_w: 目标位置（世界坐标系）
        action_history: 动作历史记录

    返回:
        torch.Tensor: 观测向量
    """
    # 获取关节索引
    joint0 = robot.find_joints("base_to_arml")[0]
    # 计算相对位置 - 目标位置与当前位置的差值
    relative_pos_w = desired_pos_w - robot.data.root_link_pos_w
    # 获取倾斜角度
    tilt_angle = robot.data.joint_pos[:, joint0[0]].unsqueeze(dim=1)

    # 获取旋转矩阵并转换为向量形式
    rot = matrix_from_quat(robot.data.root_link_quat_w)
    rot_vector = rot.reshape(-1, 9)

    # 拼接所有观测信息
    obs = torch.cat(
        [
            relative_pos_w,                                              # 相对位置 (3维)
            rot_vector,                                                  # 旋转矩阵向量 (9维)
            robot.data.root_com_lin_vel_w,                              # 线速度 (3维)
            robot.data.root_com_ang_vel_b,                              # 角速度 (3维)
            tilt_angle,                                                  # 倾斜角度 (1维)
            torch.reshape(action_history, (robot.num_instances, -1)),   # 动作历史 (变长)
        ],
        dim=-1,
    )
    return obs

def get_mpc_state(robot):
    """获取MPC控制器状态

    将机器人状态转换为MPC控制器所需的状态表示格式。
    注意：MPC使用的坐标系与Isaac Lab不同，需要进行坐标变换。

    参数:
        robot: 机器人关节对象

    返回:
        numpy.ndarray: MPC状态向量
    """
    # 获取基础状态变量
    pos, _, quat, lin_vel, ang_vel, tilt_angle = get_state(robot)

    # 坐标系变换 - 将Isaac Lab坐标系转换为MPC坐标系
    # Y和Z轴需要取反
    pos[:,[1,2]]     = -pos[:,[1,2]]      # 位置Y,Z轴取反
    quat[:,[2,3]]    = -quat[:,[2,3]]     # 四元数Y,Z分量取反
    lin_vel[:,[1,2]] = -lin_vel[:,[1,2]]  # 线速度Y,Z轴取反
    ang_vel[:,[1,2]] = -ang_vel[:,[1,2]]  # 角速度Y,Z轴取反

    # 从四元数计算欧拉角
    roll, pitch, yaw = euler_xyz_from_quat(quat)

    # 将角度限制在[-π, π]范围内
    roll = torch.fmod(roll + np.pi, 2 * np.pi) - np.pi
    pitch = torch.fmod(pitch + np.pi, 2 * np.pi) - np.pi
    yaw = torch.fmod(yaw + np.pi, 2 * np.pi) - np.pi

    # 构建MPC状态向量：[x, y, z, yaw, pitch, roll, vx, vy, vz, wx, wy, wz, tilt_angle]
    state = torch.cat(
        [
            pos,                           # 位置 (3维)
            yaw.unsqueeze(dim=1),         # 偏航角 (1维)
            pitch.unsqueeze(dim=1),       # 俯仰角 (1维)
            roll.unsqueeze(dim=1),        # 滚转角 (1维)
            lin_vel,                      # 线速度 (3维)
            ang_vel,                      # 角速度 (3维)
            tilt_angle.unsqueeze(dim=1),  # 倾斜角度 (1维)
        ],
        dim=-1,
    )
    return state.cpu().detach().numpy()

def mpc_update(solver,N_horizon,mpc_state,x_ref,u_ref,update_cost=True,phi=False):
    """MPC控制器更新函数

    执行一次MPC优化求解，计算最优控制输入。

    参数:
        solver: Acados OCP求解器实例
        N_horizon: 预测时域长度
        mpc_state: 当前MPC状态向量
        x_ref: 参考状态轨迹
        u_ref: 参考控制输入
        update_cost: 是否更新代价函数权重
        phi: 是否包含倾斜角度作为状态变量

    返回:
        tuple: (最优控制输入, 总推力, 角速度, 求解状态)
    """
    # 获取当前状态 - 分离状态变量和倾斜角度
    xcurrent = mpc_state[:-1]    # 除倾斜角度外的所有状态
    phicurrent = mpc_state[-1]   # 当前倾斜角度

    # 设置参考轨迹和参数
    for j in range(N_horizon):
        yref = np.hstack((x_ref,u_ref))  # 拼接状态和控制参考
        solver.set(j, "yref", yref)      # 设置第j步的参考
        if not phi: solver.set(j, "p", np.array([phicurrent]))  # 设置倾斜角度参数

    # 自适应代价函数权重
    if update_cost:
        Q_, R_, Qt_ = get_cost_weights(xcurrent[2],phicurrent)  # 根据高度和倾斜角度获取权重
        for j in range(N_horizon):
            solver.cost_set(j, "W", scipy.linalg.block_diag(Q_, R_))  # 设置中间阶段权重
        solver.cost_set(N_horizon, "W", Qt_)  # 设置终端权重

    # 设置初始状态约束
    if not phi:
        solver.set(0, "lbx", xcurrent)  # 设置初始状态下界
        solver.set(0, "ubx", xcurrent)  # 设置初始状态上界
    else:
        solver.set(0, "lbx", mpc_state)  # 包含倾斜角度的初始状态下界
        solver.set(0, "ubx", mpc_state)  # 包含倾斜角度的初始状态上界

    # 求解最优控制问题
    mpc_status = solver.solve()

    # 获取第一步的最优控制输入
    u_opt = solver.get(0, "u")

    # 获取预测的下一状态
    x_next = solver.get(1, "x")

    # 提取角速度信息
    omega = x_next[9:12]

    # 计算总推力 - 四个推力器推力之和
    c_des = u_opt[0] + u_opt[1] + u_opt[2] + u_opt[3]

    return u_opt, c_des, omega, mpc_status

def brtc_update(brtc,robot,c_des,omega_des):
    """BRTC控制器更新函数

    使用BRTC（Backstepping Robust Tracking Controller）控制器计算控制输入。

    参数:
        brtc: BRTC控制器实例
        robot: 机器人关节对象
        c_des: 期望总推力
        omega_des: 期望角速度

    返回:
        numpy.ndarray: 控制动作（推力器输出）
    """
    # 获取当前角速度并转换坐标系
    omega = robot.data.root_com_ang_vel_b.cpu().detach().numpy().squeeze()
    omega_transformed = np.array([omega[0], -omega[1], -omega[2]])  # Y,Z轴取反

    # 获取当前倾斜角度
    joint0 = robot.find_joints("base_to_arml")[0]  # 获取左臂关节索引
    phi = robot.data.joint_pos[:, joint0[0]].cpu().detach().numpy().squeeze()

    # 使用BRTC控制器计算控制动作
    actions = brtc.advance(
                        phi=phi,                    # 当前倾斜角度
                        omega=omega_transformed,    # 当前角速度（坐标变换后）
                        omega_des=omega_des,        # 期望角速度
                        c_des=c_des                 # 期望总推力
                )
    return actions

def mpc_update_parallel_multithread(solver_list, N_horizon, mpc_states, x_refs, u_refs, update_cost=True, max_workers=4, phi=False):
    """并行多线程MPC更新函数

    使用多线程并行执行多个MPC求解器，提高计算效率。

    参数:
        solver_list: MPC求解器列表
        N_horizon: 预测时域长度
        mpc_states: MPC状态列表
        x_refs: 参考状态轨迹列表
        u_refs: 参考控制输入列表
        update_cost: 是否更新代价函数权重
        max_workers: 最大工作线程数
        phi: 是否包含倾斜角度作为状态变量

    返回:
        tuple: (最优控制输入数组, 总推力数组, 角速度数组, 状态列表)
    """
    batch_size = len(solver_list)
    u_opt_list = [None] * batch_size   # 最优控制输入列表
    c_des_list = [None] * batch_size   # 期望总推力列表
    omega_list = [None] * batch_size   # 角速度列表
    status_list = [None] * batch_size  # 求解状态列表
    timing_list = [None] * batch_size  # 计时信息列表

    # 记录总执行时间
    total_start_time = time.time()

    # 并行执行
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {}

        for i in range(batch_size):
            # 包装MPC更新函数以记录计时信息
            def timed_mpc_update(idx=i):
                start_time = time.time()
                u_opt, c_des, omega, status = mpc_update(
                    solver_list[idx], N_horizon, mpc_states[idx], x_refs[idx], u_refs[idx], update_cost, phi
                )
                end_time = time.time()
                return u_opt, c_des, omega, status, end_time - start_time

            futures[executor.submit(timed_mpc_update)] = i

        # 收集结果
        for future in as_completed(futures):
            i = futures[future]
            try:
                u_opt, c_des, omega, status, elapsed_time = future.result()
                u_opt_list[i] = u_opt
                c_des_list[i] = c_des
                omega_list[i] = omega
                status_list[i] = status
                timing_list[i] = elapsed_time
            except Exception as e:
                print(f"求解器 {i} 执行失败，异常: {e}")
                timing_list[i] = None

    total_end_time = time.time()
    total_elapsed_time = total_end_time - total_start_time

    # 打印计时信息
    print(f"\n批处理总时间: {total_elapsed_time:.4f} 秒")
    # 过滤掉None值计算统计信息
    valid_timings = [t for t in timing_list if t is not None]
    if valid_timings:
        print(f"平均每个求解器: {np.mean(valid_timings):.4f} 秒")
        print(f"最快求解器: {np.min(valid_timings):.4f} 秒 | 最慢求解器: {np.max(valid_timings):.4f} 秒\n")
    else:
        print("所有求解器都执行失败\n")

    return np.array(u_opt_list), np.array(c_des_list), np.array(omega_list), status_list

def mpc_update_parallel(solver_list, N_horizon, mpc_states, x_refs, u_refs, update_cost=True):
    """并行MPC更新函数（串行版本）

    顺序执行多个MPC求解器，适用于不需要多线程的场景。

    参数:
        solver_list: MPC求解器列表
        N_horizon: 预测时域长度
        mpc_states: MPC状态列表
        x_refs: 参考状态轨迹列表
        u_refs: 参考控制输入列表
        update_cost: 是否更新代价函数权重

    返回:
        tuple: (最优控制输入数组, 总推力数组, 角速度数组, 状态列表)
    """
    batch_size      = len(solver_list)
    u_opt_list      = []  # 最优控制输入列表
    status_list     = []  # 求解状态列表
    c_des_list      = []  # 期望总推力列表
    omega_list      = []  # 角速度列表

    # 逐个处理每个求解器
    for i in range(batch_size):
        solver = solver_list[i]
        mpc_state = mpc_states[i]
        x_ref = x_refs[i]
        u_ref = u_refs[i]

        # 执行MPC更新
        u_opt, c_des, omega, status = mpc_update(solver,N_horizon,mpc_state,x_ref,u_ref,update_cost=update_cost)

        # 收集结果
        status_list.append(status)
        c_des_list.append(c_des)
        omega_list.append(omega)
        u_opt_list.append(u_opt)

    return np.array(u_opt_list), np.array(c_des_list), np.array(omega_list), status_list

def brtc_update_parallel(brtc, robots, c_des_list, omega_des_list):
    """并行BRTC更新函数

    为多个机器人并行执行BRTC控制器更新。

    参数:
        brtc: BRTC控制器实例
        robots: 机器人列表
        c_des_list: 期望总推力列表
        omega_des_list: 期望角速度列表

    返回:
        numpy.ndarray: 控制动作数组
    """
    batch_size = len(robots)
    actions_list = []

    for i in range(batch_size):
        robot = robots[i]
        c_des = c_des_list[i]
        omega_des = omega_des_list[i]

        # 获取当前角速度并转换坐标系
        omega = robot.data.root_com_ang_vel_b.cpu().detach().numpy().squeeze()
        omega_transformed = np.array([omega[0], -omega[1], -omega[2]])  # Y,Z轴取反

        # 获取当前倾斜角度
        joint0 = robot.find_joints("base_to_arml")[0]  # 获取左臂关节索引
        phi = robot.data.joint_pos[:, joint0[0]].cpu().detach().numpy().squeeze()

        # 使用BRTC控制器计算控制动作
        actions = brtc.advance(
            phi=phi,                    # 当前倾斜角度
            omega=omega_transformed,    # 当前角速度（坐标变换后）
            omega_des=omega_des,        # 期望角速度
            c_des=c_des                 # 期望总推力
        )
        actions_list.append(actions)

    return np.array(actions_list)

def initialize_solvers(num_envs, N_horizon, acados_ocp_path, build_mpc, env_origins, x0, u0, phi=False):
    env_origins = env_origins.cpu().detach().numpy()
    solver_list = []
    for i in range(num_envs):
        if not phi:
            ocp = create_ocp_solver_description()
        else:
            ocp = create_ocp_solver_description_phi()
        solver = AcadosOcpSolver(
            ocp,
            json_file=os.path.join(acados_ocp_path, ocp.model.name + '_acados_ocp.json'),
            generate=build_mpc,
            build=build_mpc
        )

        # set initial state and reference
        x0[0] = env_origins[i,0]
        x0[1] = -env_origins[i,1]
        x0[2] = -env_origins[i,2]
        for stage in range(N_horizon + 1):
            solver.set(stage, "x", x0)
            if not phi: solver.set(stage, "p", np.array([0.0]))
        for stage in range(N_horizon):
            solver.set(stage, "u", u0)

        solver_list.append(solver)

    return solver_list

def phi_ref(z):
    phi_ref_ = np.zeros_like(z)
    mask = (z >= 0.0) & (z < 1.0)
    phi_ref_[mask] = (1.0 - z[mask]) * (np.pi / 2)
    return phi_ref_

def traj_descent_time(t,phi,z,z_star,num_envs,root_position):
    root_position = root_position.cpu().detach().numpy()
    descent_vel = 0.5
    z0 = -root_position[:,2]
    z = np.clip(z0 + descent_vel*t, -2.0, 0.0)
    dz = descent_vel * np.ones_like(z)
    x_ref = np.zeros((num_envs,12))
    x_ref[:,0] = root_position[:,0]
    x_ref[:,1] = -root_position[:,1]
    x_ref[:,2] = z
    x_ref[:,8] = dz
    u_ref = np.zeros((num_envs,4))
    accept_height = np.abs(z) < np.abs(z_star)
    accept = phi < np.deg2rad(50)
    np.logical_or(accept,accept_height,accept)
    tilt_vel = accept * np.ones((num_envs,))
    tilt_vel = np.expand_dims(tilt_vel, axis=1)
    # phi_ref_ = phi_ref(z)
    # phi_ref_ = np.expand_dims(phi_ref_, axis=1)
    phi_ref_ = np.ones_like(z) * np.deg2rad(70)
    phi_ref_ = np.expand_dims(phi_ref_, axis=1)
    return x_ref,u_ref,phi_ref_,tilt_vel

def plot_heatmap(fig,ax,scales,angles,metric,run_folder,run_name,plot_name='impact_heatmap',vmin=0.5,vmax=2.5,cmap='RdYlGn_r'):
    # Normalize metric
    norm = plt.Normalize(vmin=vmin, vmax=vmax)

    # Compute circle coordinates
    x = scales * np.cos(angles)
    y = scales * np.sin(angles)

    # Create grid for heatmap focused within the quarter circle
    grid_resolution = 1000
    theta_grid = np.linspace(0, np.pi / 2, grid_resolution)
    radius_grid = np.linspace(0, np.max(scales), grid_resolution)
    Theta, Radius = np.meshgrid(theta_grid, radius_grid)
    X_grid = Radius * np.cos(Theta)
    Y_grid = Radius * np.sin(Theta)

    # Interpolate final_distance over the grid
    heatmap = griddata((x, y), metric, (X_grid, Y_grid), method='cubic')

    # Plot heatmap background
    c = ax.pcolormesh(X_grid, Y_grid, heatmap, cmap=cmap, norm=norm, shading='auto', alpha=0.8, rasterized=True)

    # Create scatter plot
    _ = ax.scatter(x, y, c=metric.flatten(), cmap=cmap, norm=norm, s=50, edgecolor='k')

    # Add colorbar
    cbar = fig.colorbar(c, ax=ax)
    cbar.ax.tick_params(labelsize=14)

    # Plot quarter-circle arcs for force magnitude
    theta = np.linspace(0, np.pi / 2, 100)  # First quadrant angles
    for radius in scales:
        x_arc = radius * np.cos(theta)
        y_arc = radius * np.sin(theta)
        ax.plot(x_arc, y_arc, color='gray', linestyle='--', linewidth=1)
        # Label the arc at 45 degrees
        label_x = (radius / np.sqrt(2))
        label_y = (radius / np.sqrt(2))
        # ax.text(label_x, label_y, f'{radius:.2f}', fontsize=14, ha='center', va='center')

    # Format plot
    ax.set_aspect('equal')
    ax.set_xlim(0, np.max(scales))
    ax.set_ylim(0, np.max(scales))
    ax.grid(False)

    plt.tight_layout()
    fname = os.path.join(run_folder, f"{plot_name}_{run_name}.png")
    fig.savefig(fname, dpi=150, bbox_inches='tight')

"""Main function."""
def main():   
    # get parameters
    num_envs                    = params_.get('num_envs')
    mass_deviations             = params_.get('mass_deviations')
    spin_direction              = params_.get('spin_direction')
    vis_markers                 = params_.get('vis_markers')
    rl_path                     = params_.get('rl_path')
    N_horizon                   = params_.get('N_horizon')
    acados_ocp_path             = params_.get('acados_ocp_path')
    build_mpc                   = params_.get('build_mpc')
    v_max_absolute              = params_.get('v_max_absolute')
    control_algorithm           = params_.get('control_algorithm')
    disturb                     = params_.get('disturb')
    disturbance_type            = params_.get('disturbance_type')
    quantize_tilt_actions       = params_.get('quantize_tilt_actions')
    sim_dt                      = params_.get('sim_dt')
    decimation                  = params_.get('decimation')
    action_update_rate          = params_.get('action_update_rate')
    observation_delay           = params_.get('observation_delay')
    sim_time                    = params_.get('sim_time')
    sim_steps                   = params_.get('sim_steps')
    alpha                       = params_.get('alpha')
    white_force_scale           = params_.get('white_force_scale')
    white_moment_scale          = params_.get('white_moment_scale')
    push_time                   = params_.get('push_time')
    push_duration               = params_.get('push_duration')
    desired_position            = params_.get('desired_position')
    initial_pose                = params_.get('initial_pose')
    initial_twist               = params_.get('initial_twist')
    kT                          = params_.get('kT')
    kM                          = params_.get('kM')
    thruster_effectiveness      = params_.get('thruster_effectiveness')
    z_star                      = params_.get('z_star')

    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(dt=sim_dt, device=args_cli.device, gravity=(0.0, 0.0, -9.81))
    sim = SimulationContext(sim_cfg)
    
    # Set main camera
    sim.set_camera_view(eye=[5.0, 2.5, 2.5], target=[0.0, 0.0, 0.75])

    # Setup the scene
    scene_cfg = ATMOSceneCfg(num_envs=num_envs, env_spacing=2.0)
    scene = InteractiveScene(scene_cfg)

    # Play the simulator
    sim.reset()

    # Now we are ready!
    print("[INFO]: Setup complete...")

    # get robot
    robot = scene["robot"]

    # get rl model from rl_path
    rl = ort.InferenceSession(rl_path)

    # get run_name
    run_name = control_algorithm

    # define the MPC solver for each environment
    if control_algorithm == 'mpc' or control_algorithm == 'mpc-brtc':
        x0 = np.zeros(12)
        u0 = np.zeros(4)
        solvers = initialize_solvers(num_envs, N_horizon, acados_ocp_path, build_mpc, scene.env_origins,x0,u0)

    if control_algorithm == 'mpc-phi':
      x0 = np.zeros(13)
      u0 = np.zeros(5)
      solvers = initialize_solvers(num_envs, N_horizon, acados_ocp_path, build_mpc, scene.env_origins,x0,u0,phi=True)

    # initialize BRTC controller
    if control_algorithm == 'rl-brtc' or control_algorithm == 'mpc-brtc' or control_algorithm == 'brtc':
        brtc = BRTC(h=sim_dt)

    # create push force direction with direction ranging from 0 to pi/2 for each environment
    sqrt_num_envs = int(np.sqrt(num_envs))
    push_scale = torch.linspace(4 * params_['kT'] * 0.15, 4 * params_['kT'] * 2.0 , int(num_envs/sqrt_num_envs), device=args_cli.device)
    push_angle = torch.linspace(0.0, np.pi/2, int(num_envs/sqrt_num_envs), device=args_cli.device)
    angles, scales = torch.meshgrid(push_angle, push_scale)
    angles = angles.reshape(num_envs,1)
    scales = scales.reshape(num_envs,1)
    push_direction = torch.cat([torch.cos(angles), torch.sin(angles), torch.zeros_like(angles)],dim=-1).unsqueeze(1)

    # Get bodies
    rotor0    = robot.find_bodies("rotor0")[0][0]
    rotor1    = robot.find_bodies("rotor1")[0][0]
    rotor2    = robot.find_bodies("rotor2")[0][0]
    rotor3    = robot.find_bodies("rotor3")[0][0]
    base_link = robot.find_bodies("base_link")[0][0]
    arml      = robot.find_bodies("arml")[0][0]
    armr      = robot.find_bodies("armr")[0][0]

    # Get joints
    joint0 = robot.find_joints("base_to_arml")[0][0]
    joint1 = robot.find_joints("base_to_armr")[0][0]

    # set mass of arml
    masses              = robot.root_physx_view.get_masses()
    masses[0,base_link] = mass_deviations[0] * masses[0,base_link]
    masses[0,arml]      = mass_deviations[1] * masses[0,arml]
    masses[0,armr]      = mass_deviations[2] * masses[0,armr]
    robot.root_physx_view.set_masses(masses, torch.tensor([base_link,arml,armr,rotor1,rotor2,rotor0,rotor3]))

    # Define tensors
    thrust = torch.zeros(robot.num_instances, 4, 3, device=args_cli.device)
    moment = torch.zeros(robot.num_instances, 4, 3, device=args_cli.device)

    # initialize joint pos
    joint_pos = robot.data.default_joint_pos
    joint_vel = robot.data.default_joint_vel

    # initialize actions
    actions = torch.ones(robot.num_instances, 5, device=args_cli.device)
    filtered_actions = torch.zeros(robot.num_instances, 5, device=args_cli.device)

    # action history
    action_space = 5
    action_history_length = 10
    action_history = torch.zeros(robot.num_instances, action_history_length, action_space, device=args_cli.device)

    # observation buffer
    observation_history = 0
    num_obs = 19 + action_space * action_history_length
    observation_buffer = torch.zeros(robot.num_instances, 5, num_obs, device=args_cli.device)

    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    sim_time = 0.0
    count = 0

    # Define visualizer
    if vis_markers: visualizer = define_markers()

    # Log actions and observations and time and plot them when simulation is done
    times_log            = []
    actions_log          = []
    filtered_actions_log = []
    observations_log     = []
    mpc_state_log        = []
    x_ref_log            = []

    pos_log = []
    euler_log = []
    lin_vel_log = []
    ang_vel_log = []
    tilt_angle_log = []
    status_log = []

    # Simulate physics
    while simulation_app.is_running() and count < sim_steps:
        # reset
        if count % sim_steps == 0:
            # reset counters
            sim_time = 0.0
            count = 0

            # reset dof state
            root_state = robot.data.default_root_state.clone()
            root_state[:, :7] = initial_pose
            root_state[:, :3] += scene.env_origins
            root_state[:,7:]   = initial_twist
            robot.write_root_pose_to_sim(root_state[:, :7])
            robot.write_root_velocity_to_sim(root_state[:, 7:])

            # set desired positions
            desired_position = desired_position.repeat(robot.num_instances,1)
            desired_position += scene.env_origins

            # set joint positions 
            joint_pos, joint_vel = robot.data.default_joint_pos.clone(), robot.data.default_joint_vel.clone()
            robot.write_joint_state_to_sim(joint_pos, joint_vel)

            # clear internal buffers
            scene.reset()
            print(">>>>>>>> Reset!")

        # get and log state
        pos, euler, _, lin_vel, ang_vel, tilt_angle = get_state(robot)
        pos_log.append(pos.cpu().detach().numpy())
        euler_log.append(euler.cpu().detach().numpy())
        lin_vel_log.append(lin_vel.cpu().detach().numpy())
        ang_vel_log.append(ang_vel.cpu().detach().numpy())
        tilt_angle_log.append(tilt_angle.cpu().detach().numpy())

        x_ref                = np.zeros(12)
        obs                  = np.zeros((robot.num_instances,num_obs))
        if count % decimation == 0:
            match control_algorithm:
                case "rl":
                    # get rl observations at the correct rate       
                    if count % action_update_rate == 0:
                        action_history     = torch.cat([actions.clone().unsqueeze(dim=1), action_history[:, :-1]], dim=1)
                    obs_current        = get_observations(robot, desired_position, action_history)
                    observation_buffer = torch.cat([obs_current.unsqueeze(1),observation_buffer[:, :-1]], dim=1)
                    obs                = torch.reshape(observation_buffer[:, observation_delay:observation_delay + observation_history + 1],(1,-1))
                    obs                = obs.cpu().detach().numpy().reshape(robot.num_instances,-1)
                    outputs              = rl.run(None, {"obs": obs.astype(np.float32)})
                    actions              = outputs[0]
                    actions              = torch.tensor(actions, device=args_cli.device)
                case "mpc":
                    mpc_state                  = get_mpc_state(robot)
                    x_ref,u_ref,_,tilt_vel     = traj_descent_time(sim_time,mpc_state[:,-1],mpc_state[:,2],z_star,num_envs,root_state[:, :3])
                    mpc_state_log.append(mpc_state)
                    actions, _, _, status      = mpc_update_parallel_multithread(solvers, N_horizon, mpc_state, x_ref, u_ref, update_cost=True)
                    status_log.append(status)
                    actions                    = np.hstack((actions,tilt_vel))
                    actions                    = torch.tensor(actions, device=args_cli.device)
                    # if height is lower than 0.3 set actions of that environment to zero
                    print("height ", np.abs(mpc_state[0,2]))
                    for i in range(num_envs):
                        if np.abs(mpc_state[i,2]) < 0.2:
                            actions[i,:-1] = torch.zeros_like(actions[i,:-1])

                    # if status is not zero for any of the environments set the actions of that environment to zero
                    for i in range(num_envs):
                        if status[i] != 0:
                            actions[i,:-1] = torch.zeros_like(actions[i,:-1])
                case "mpc-phi":
                    mpc_state                  = get_mpc_state(robot)
                    x_ref,u_ref,phi_ref,tilt_vel       = traj_descent_time(sim_time,mpc_state[:,-1],mpc_state[:,2],z_star,num_envs,root_state[:, :3])
                    mpc_state_log.append(mpc_state)
                    x_ref = np.hstack((x_ref,phi_ref))
                    u_ref = np.hstack((u_ref,np.array([[0.0]])))
                    actions, _, _, status      = mpc_update_parallel_multithread(solvers, N_horizon, mpc_state, x_ref, u_ref, update_cost=False, phi=True)
                    status_log.append(status)
                    actions                    = np.hstack((actions,tilt_vel))
                    actions                    = torch.tensor(actions, device=args_cli.device)
                case "rl-brtc":
                    x_ref                = np.zeros(12)
                    outputs              = rl.run(None, {"obs": obs.astype(np.float32)})
                    mu                   = outputs[0]
                    actions              = torch.tensor(mu, device=args_cli.device)
                    actions              = brtc_update(brtc,robot,actions[:,0],actions[:,1:4])
                    actions              = np.hstack((actions, np.array([actions[:,-1]]))) + 1e-6
                    actions              = torch.tensor(actions, device=args_cli.device).unsqueeze(0)
                case "mpc-brtc":
                    x_ref,u_ref,tilt_vel     = traj_descent_time(sim_time)
                    mpc_state                = get_mpc_state(robot)
                    c_des, omega_des, status = mpc_update(solver, N_horizon, mpc_state, x_ref, u_ref,update_cost=True,brtc=True)
                    actions                  = brtc_update(brtc,robot,c_des,omega_des)
                    if mpc_state[-1] > np.deg2rad(70): 
                         tilt_vel = 0.0
                    if status != 0: 
                         actions = np.zeros_like(actions)
                    actions                  = np.hstack((actions, np.array([tilt_vel]))) + 1e-6
                    actions                  = torch.tensor(actions, device=args_cli.device).unsqueeze(0)
                case "brtc":
                    x_ref             = np.zeros(12)   # dummy reference
                    omega_des         = np.array([0.0,0.0,0.0])
                    c_des             = 0.0
                    tilt_vel          = 0.0
                    actions           = brtc_update(brtc,robot,c_des,omega_des)
                    actions           = np.hstack((actions, np.array([tilt_vel]))) + 1e-6
                    actions           = torch.tensor(actions, device=args_cli.device).unsqueeze(0)
                case _:
                    print("Invalid control algorithm: select from rl, mpc, rl-brtc, mpc-brtc")
                    exit()

        # log observations actions and times
        observations_log.append(obs)
        times_log.append(sim_time)
        actions_log.append(actions.cpu().detach().numpy())
        x_ref_log.append(x_ref)

        # Apply low-pass filter
        filtered_actions[:, :4] = low_pass_filter(actions[:, :4], filtered_actions[:, :4], alpha)
        filtered_actions[:, 4]  = actions[:, 4]

        # log the filtered actions
        filtered_actions_log.append(filtered_actions.cpu().detach().numpy())

        # Assign the thrust to each of the rotors
        thrust[:,:,2] = (thruster_effectiveness * kT * filtered_actions.reshape(robot.num_instances, 1, 5)[:, :, :4]).squeeze()
        moment[:,:,2] = spin_direction * kM * thrust[:, :, 2]

        # get the tilt action
        if quantize_tilt_actions: tilt_action = torch.round(filtered_actions[:, 4])
        else: tilt_action     = filtered_actions[:, 4]
        tilt_action = tilt_action.reshape(robot.num_instances,1)

        # Update the joint position
        joint_pos  = joint_pos + v_max_absolute * tilt_action * sim_dt
        joint_pos  = torch.clamp(joint_pos,0.0,torch.pi/2)
        joint_vel  = v_max_absolute * tilt_action

        # Apply disturbance
        dist_force         = torch.zeros(robot.num_instances, 1, 3, device=args_cli.device)
        dist_moment        = torch.zeros(robot.num_instances, 1, 3, device=args_cli.device)        
        if disturb:
            match disturbance_type:
                case "white":
                    dist_force        = torch.zeros_like(dist_force).uniform_(-white_force_scale, white_force_scale)
                    dist_moment       = torch.zeros_like(dist_moment).uniform_(-white_moment_scale, white_moment_scale)
                case "push":
                    if sim_time > push_time and sim_time < push_time + push_duration:
                        dist_force         = push_direction * scales.unsqueeze(1)
                case _:
                    print("Invalid disturbance type: select from white and push")
                    exit()            

        # Get total force and moment
        total_force  = torch.cat([thrust, dist_force], dim=1)
        total_moment = torch.cat([moment, dist_moment], dim=1)

        # round force down if it is less than 1e-4
        print("joint_vel ", joint_vel)
        print("joint_pos ", joint_pos)

        # Set force, torque, joint velocity and joint position targets
        robot.set_external_force_and_torque(total_force, total_moment, body_ids=[rotor0,rotor1,rotor2,rotor3,base_link])
        robot.set_joint_velocity_target(joint_vel, joint_ids=[joint0,joint1])
        robot.set_joint_position_target(joint_pos, joint_ids=[joint0,joint1])

        # Write data to sim
        scene.write_data_to_sim()

        # Visualize markers
        if vis_markers: visualize_markers(robot,desired_position,visualizer)

        # perform step
        sim.step()

        # update sim-time
        sim_time += sim_dt
        count    += 1

        # update buffers
        scene.update(sim_dt)

    return status_log, scales, angles, times_log, pos_log, euler_log, lin_vel_log, ang_vel_log, tilt_angle_log, observations_log, mpc_state_log, actions_log, filtered_actions_log, x_ref_log, run_name

if __name__ == "__main__":
    # run the main function
    status_log, scales, angles, times_log, pos_log,euler_log,lin_vel_log,ang_vel_log,tilt_angle_log, observations_log, mpc_state_log, actions_log, filtered_actions_log, x_ref_log, run_name = main()

    # Generate a timestamped folder
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    run_folder = f"/home/<USER>/src/IsaacLab/source/standalone/demos/data/run_{run_name}_{timestamp}"
    os.makedirs(run_folder, exist_ok=True)

    # plot the data
    # plot_data(actions_log, filtered_actions_log, pos_log,euler_log,lin_vel_log,ang_vel_log,tilt_angle_log, times_log, x_ref_log)

    from scipy.interpolate import griddata

    # Enable LaTeX rendering and set font sizes
    plt.rcParams.update({
        'text.usetex': True,
        'font.size': 16,
        'axes.labelsize': 18,
        'axes.titlesize': 20,
        'xtick.labelsize': 14,
        'ytick.labelsize': 14,
        'legend.fontsize': 16
    })

    # Compute the impact velocity for each environment
    pos = np.array(pos_log).squeeze()
    lin_vel = np.array(lin_vel_log).squeeze()
    status = np.array(status_log).squeeze()
    impact_vel = []
    failed = []
    for i in range(pos.shape[1]):
        idx = np.where(pos[:, i, 2] < 0.3)[0][0]
        # # check where status is zero and if array is empty then not failed, else failed
        # if np.where(status[:, i] != 0)[0].size == 0:
        #     failed.append(0)
        # else:
        #     failed.append(1)
        impact_vel.append(np.linalg.norm(lin_vel[idx, i, :]))
    impact_vel = np.abs(np.array(impact_vel))
    # failed = np.array(failed)

    # Process data
    scales = scales.squeeze().squeeze().cpu().detach().numpy().squeeze()
    angles = angles.squeeze().squeeze().cpu().detach().numpy().squeeze()
    final_pos = pos[-1, :, :]
    initial_pos = pos[0, :, :]
    final_distance = np.linalg.norm(final_pos[:,:2] - initial_pos[:,:2], axis=1)


    # Figure 1: impact vel heatmap
    metric = impact_vel
    fig, ax = plt.subplots(figsize=(8, 6))
    plot_heatmap(fig,ax,scales,angles,metric,run_folder,run_name,plot_name='impact_heatmap',vmin=0.5,vmax=2.5,cmap='RdYlGn_r')

    # Figure 2: final distance heatmap
    metric = final_distance
    fig, ax = plt.subplots(figsize=(8, 6))
    plot_heatmap(fig,ax,scales,angles,metric,run_folder,run_name,plot_name='dist_heatmap',vmin=0.0,vmax=2.0,cmap='coolwarm')

    # Save data in an HDF5 file inside the timestamped folder
    h5_path = os.path.join(run_folder, f"data_{run_name}.h5")
    with h5py.File(h5_path, 'w') as file:
        file.create_dataset("times", data=times_log)
        file.create_dataset("observations", data=observations_log)
        file.create_dataset("actions", data=actions_log)
        file.create_dataset("filtered_actions", data=filtered_actions_log)
        file.create_dataset("x_ref", data=x_ref_log)

    # Convert NumPy/PyTorch objects before saving YAML
    def convert_tensors_and_arrays(obj):
        """Recursively convert NumPy arrays, NumPy scalars, and PyTorch tensors to native types."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, torch.Tensor):
            return obj.tolist()
        elif isinstance(obj, (np.floating, np.integer)):  # Handle NumPy scalars
            return obj.item()
        elif isinstance(obj, dict):
            return {key: convert_tensors_and_arrays(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_tensors_and_arrays(item) for item in obj]
        return obj

    params_serialized = convert_tensors_and_arrays(params_)

    # Save parameters in a YAML file inside the timestamped folder
    yaml_path = os.path.join(run_folder, f"params_{run_name}.yaml")
    with open(yaml_path, 'w') as file:
        yaml.dump(params_serialized, file)

    print(f"Data and parameters saved in {run_folder}")

    plt.show()
    # close sim app
    simulation_app.close()
