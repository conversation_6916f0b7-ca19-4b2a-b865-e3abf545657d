﻿omni.isaac.lab.sim
==================

.. automodule:: omni.isaac.lab.sim

  .. rubric:: Submodules

  .. autosummary::

    converters
    schemas
    spawners
    utils

  .. rubric:: Classes

  .. autosummary::

    SimulationContext
    SimulationCfg
    PhysxCfg
    RenderCfg

  .. rubric:: Functions

  .. autosummary::

    simulation_context.build_simulation_context

Simulation Context
------------------

.. autoclass:: SimulationContext
  :members:
  :show-inheritance:

Simulation Configuration
------------------------

.. autoclass:: SimulationCfg
  :members:
  :show-inheritance:
  :exclude-members: __init__

.. autoclass:: PhysxCfg
  :members:
  :show-inheritance:
  :exclude-members: __init__

.. autoclass:: RenderCfg
  :members:
  :show-inheritance:
  :exclude-members: __init__

Simulation Context Builder
--------------------------

.. automethod:: simulation_context.build_simulation_context

Utilities
---------

.. automodule:: omni.isaac.lab.sim.utils
  :members:
  :show-inheritance:
