# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
ATMO强化学习环境

此文件实现了ATMO（Aerial Tilting Multirotor Omnicopter）机器人的强化学习训练环境。
环境支持多种训练配置，包括随机化、干扰、噪声等，用于训练鲁棒的飞行控制策略。

主要功能：
- 多环境并行训练
- 物理参数随机化
- 外部干扰模拟
- 观测噪声注入
- 奖励函数设计
- 终止条件检测
"""

from __future__ import annotations

import gymnasium as gym                    # OpenAI Gym环境接口
import torch                              # PyTorch深度学习框架
from numpy import pi, exp, copy          # 数学常数和函数
from IPython import embed                # 交互式调试工具

# Isaac Lab核心模块
import omni.isaac.lab.sim as sim_utils                                    # 仿真工具
from omni.isaac.lab.assets import Articulation, ArticulationCfg           # 关节机器人资产
from omni.isaac.lab.envs import DirectRLEnv, DirectRLEnvCfg              # 直接强化学习环境
from omni.isaac.lab.envs.ui import BaseEnvWindow                         # 环境UI窗口
from omni.isaac.lab.markers import VisualizationMarkers                  # 可视化标记
from omni.isaac.lab.scene import InteractiveSceneCfg                     # 交互式场景配置
from omni.isaac.lab.sim import SimulationCfg                             # 仿真配置
from omni.isaac.lab.terrains import TerrainImporterCfg                   # 地形导入配置
from omni.isaac.lab.utils import configclass                             # 配置类装饰器
from omni.isaac.lab.utils.math import euler_xyz_from_quat, quat_from_euler_xyz, quat_rotate, matrix_from_quat  # 数学工具
from omni.isaac.lab.sensors import ContactSensorCfg, ContactSensor       # 接触传感器

##
# 预定义配置
##
from omni.isaac.lab_assets import ATMO_CFG            # isort: skip  # ATMO机器人配置
from omni.isaac.lab.markers import CUBOID_MARKER_CFG  # isort: skip  # 立方体标记配置

class ATMOEnvWindow(BaseEnvWindow):
    """ATMO环境窗口管理器

    为ATMO环境提供图形用户界面，包括调试可视化和参数控制。
    继承自BaseEnvWindow，提供标准的环境窗口功能。
    """

    def __init__(self, env: ATMOEnv, window_name: str = "IsaacLab"):
        """初始化窗口

        参数:
            env: ATMO环境对象
            window_name: 窗口名称，默认为"IsaacLab"
        """
        # 初始化基础窗口
        super().__init__(env, window_name)
        # 添加自定义UI元素
        with self.ui_window_elements["main_vstack"]:
            with self.ui_window_elements["debug_frame"]:
                with self.ui_window_elements["debug_vstack"]:
                    # 添加目标可视化UI元素
                    self._create_debug_vis_ui_element("targets", self.env)

@configclass
class ATMOEnvCfg(DirectRLEnvCfg):
    """ATMO环境配置类

    定义ATMO强化学习环境的所有配置参数，包括训练设置、物理参数、
    奖励权重、随机化参数等。
    """

    # ========== 高级标志位 ==========
    randomize                      = True   # 启用随机化（域随机化）
    terminate                      = True   # 启用终止条件检测
    disturb                        = True   # 启用外部干扰
    noise                          = True   # 启用观测噪声
    actuator_dynamics              = True   # 启用执行器动力学模拟
    randomize_motor_dynamics       = False  # 随机化电机动力学参数
    quantize_tilt_action           = False  # 量化倾斜动作（离散化）
    curriculum_update_rate         = 8e3    # 课程学习更新频率
    curriculum_steps_to_completion = curriculum_update_rate * 10  # 课程学习完成步数

    # ========== 动作和观测历史 ==========
    action_history_length = 10       # 动作历史长度
    observation_history_length = 0   # 观测历史长度

    # ========== 环境基础参数 ==========
    episode_length_s                         = 5.0    # 每个回合长度（秒）
    sim_dt                                   = 1/50   # 仿真时间步长（训练时通常用1/100）
    decimation                               = 1      # 控制频率降采样（训练时通常用2）
    action_space                             = 5      # 动作空间维度（4个推力器 + 1个倾斜角）

    # ========== 观测空间配置 ==========
    num_obs                                  = 19 + action_space * action_history_length  # 观测维度
    observation_space                        = (observation_history_length + 1) * num_obs  # 总观测空间

    # ========== 其他配置 ==========
    num_privileged_obs                       = 12     # 特权观测维度（用于评论家网络）
    state_space                              = 0      # 状态空间维度
    debug_vis                                = True   # 启用调试可视化
    num_envs                                 = 4096   # 并行环境数量

    ui_window_class_type = ATMOEnvWindow  # UI窗口类型

    # ========== 仿真配置 ==========
    sim: SimulationCfg = SimulationCfg(
        dt=sim_dt,                              # 仿真时间步长
        render_interval=decimation,             # 渲染间隔（降采样）
        disable_contact_processing=True,        # 禁用接触处理（提高性能）
        # 物理材质配置
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",   # 摩擦力组合模式：相乘
            restitution_combine_mode="multiply", # 恢复系数组合模式：相乘
            static_friction=1.0,                # 静摩擦系数
            dynamic_friction=1.0,               # 动摩擦系数
            restitution=0.0,                    # 恢复系数（弹性碰撞程度）
        ),
    )

    # ========== 地形配置 ==========
    terrain = TerrainImporterCfg(
        prim_path="/World/ground",              # 地形在场景中的路径
        terrain_type="plane",                   # 地形类型：平面
        collision_group=-1,                     # 碰撞组（-1表示与所有组碰撞）
        # 地面物理材质配置
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",   # 摩擦力组合模式：相乘
            restitution_combine_mode="multiply", # 恢复系数组合模式：相乘
            static_friction=1.0,                # 静摩擦系数
            dynamic_friction=1.0,               # 动摩擦系数
            restitution=0.0,                    # 恢复系数（无弹性碰撞）
        ),
        debug_vis=False,                        # 禁用调试可视化
    )

    # ========== 场景配置 ==========
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=num_envs,          # 并行环境数量
        env_spacing=2.5,            # 环境间距（米）
        replicate_physics=True      # 复制物理设置到所有环境
    )

    # ========== 机器人配置 ==========
    robot: ArticulationCfg = ATMO_CFG.replace(prim_path="/World/envs/env_.*/Robot")

    # ========== 接触传感器配置 ==========
    contact_sensor: ContactSensorCfg = ContactSensorCfg(
        prim_path="/World/envs/env_.*/Robot/.*",  # 传感器路径（机器人所有部件）
        track_air_time=True,                      # 跟踪空中时间
        history_length=2                          # 历史记录长度
    )

    # ========== 终止条件 ==========
    too_fast_vel                       = 2.0   # 速度过快终止阈值（m/s）
    termination_dxy                    = 1.50  # 水平距离终止阈值（m）
    termination_height                 = 3.0   # 高度终止阈值（m）

    # ========== 观测延迟配置 ==========
    observation_buffer_length          = 5     # 观测缓冲区长度
    observation_delay                  = 1     # 观测延迟步数

    # ========== 接受状态半径 ==========
    delta_d                            = 0.40  # 着陆接受半径（m）

    # ========== 期望速度 ==========
    vx_des, vy_des, vz_des             = 0.0, 0.0, -0.50  # 期望线速度（m/s）

    # ========== 奖励权重 - 惩罚项 ==========
    lin_vel_pen_scale                  = -0.10  # 线速度惩罚权重
    ang_vel_pen_scale                  = -0.30  # 角速度惩罚权重
    spin_pen_scale                     = -0.30  # 自旋惩罚权重
    action_rate_pen_scale              = -0.80  # 动作变化率惩罚权重
    ground_thrust_pen_scale            = -0.13  # 着地后推力惩罚权重
    orientation_pen_scale              = -0.10  # 姿态惩罚权重

    # ========== 奖励权重 - 严重惩罚 ==========
    impulse_pen                        = -1.0   # 冲击惩罚
    died_pen                           = -2.0   # 死亡惩罚

    # ========== 奖励权重 - 正向奖励 ==========
    distance_to_goal_xy_rew_scale      = 0.30   # 水平距离目标奖励权重
    descending_rew_scale               = 0.30   # 下降奖励权重
    tilt_rew_scale                     = 0.80   # 倾斜奖励权重
    contact_in_acceptance_rew_scale    = 0.40   # 接受区域内接触奖励权重

    # ========== 标称参数 ==========
    kT_0           = 28.15      # 标称推力系数
    kM_0           = 0.018      # 标称力矩系数
    max_tilt_vel_0 = pi / 8     # 标称最大倾斜速度（rad/s）

    # ========== 随机干扰力和力矩尺度 ==========
    disturbance_force_scale            = 4 * kT_0 * 0.50        # 干扰力尺度（最佳值0.05）
    disturbance_moment_scale           = 4 * kT_0 * kM_0 * 0.05 # 干扰力矩尺度（最佳值0.05）

    # ========== 连续干扰尺度 ==========
    dist_force_cts_scale              = 4 * kT_0 * 0.0          # 连续干扰力尺度
    dist_moment_cts_scale             = 4 * kT_0 * kM_0 * 0.0   # 连续干扰力矩尺度

    # ========== 随机化参数 ==========
    kT_error_scale                     = 0.2       # 推力系数误差尺度
    kM_error_scale                     = 0.2       # 力矩系数误差尺度
    max_tilt_vel_error_scale           = 0.2       # 最大倾斜速度误差尺度
    initial_height_range               = [1.0, 2.0]    # 初始高度范围（m）
    initial_lin_vel_range              = [-0.1, 0.1]   # 初始线速度范围（m/s）
    initial_ang_vel_range              = [-0.1, 0.1]   # 初始角速度范围（rad/s）
    initial_tilt_range                 = [0.0, pi/6]   # 初始倾斜角度范围（rad）
    initial_tilt_vel_range             = [0.0, 1.0]    # 初始倾斜速度范围（rad/s）

    # ========== 低通滤波器常数 ==========
    step_dt                             = sim_dt * decimation   # 控制步长
    T_m_range                           = [0.1, 0.2]            # 电机时间常数范围
    T_m_0                               = 0.15                  # 标称电机时间常数
    alpha_0                             = 1.0 - exp(-step_dt / T_m_0).item()  # 标称滤波系数
    alpha_range                         = [1.0 - exp(-step_dt / T_m_range[1]).item(), 1.0 - exp(-step_dt / T_m_range[0]).item()]  # 滤波系数范围

    # ========== 观测噪声尺度 ==========
    pos_noise_scale                    = 0.005      # 位置噪声尺度（0.5 cm）
    quat_noise_scale                   = 0.005      # 四元数噪声尺度（0.5%）
    lin_vel_noise_scale                = 0.035      # 线速度噪声尺度（0.035 m/s）
    ang_vel_noise_scale                = 0.035      # 角速度噪声尺度（2 deg/s 或 0.035 rad/s）
    tilt_noise_scale                   = 0.018      # 倾斜角噪声尺度（2度 或 0.18 rad/s）
    roll_noise_scale                   = 0.008      # 滚转角噪声尺度（2度 或 0.18 rad/s）
    pitch_noise_scale                  = 0.008      # 俯仰角噪声尺度（2度 或 0.18 rad/s）
    rot_noise_scale                    = 0.005      # 旋转噪声尺度（0.5%）

class ATMOEnv(DirectRLEnv):
    """ATMO强化学习环境类

    实现ATMO机器人的强化学习训练环境，包括状态管理、动作执行、
    奖励计算、终止条件检测等核心功能。
    """
    cfg: ATMOEnvCfg

    def __init__(self, cfg: ATMOEnvCfg, render_mode: str | None = None, **kwargs):
        """初始化ATMO环境

        参数:
            cfg: 环境配置对象
            render_mode: 渲染模式
            **kwargs: 其他关键字参数
        """
        super().__init__(cfg, render_mode, **kwargs)

        # ========== 观测缓冲区 ==========
        # 用于存储历史观测数据，支持观测延迟
        self._observation_buffer = torch.zeros(self.num_envs, self.cfg.observation_buffer_length, self.cfg.num_obs, device=self.device)

        # ========== 课程学习相关 ==========
        self.box_extent = 0.1                      # 目标区域范围
        self.curriculum_update_time = 0             # 课程更新时间
        self.distance_to_goal_epoch_av = 0.0        # 到目标距离的平均值

        # ========== 状态标志位 ==========
        self._current_contacts = torch.zeros(self.num_envs, dtype=torch.bool, device=self.device)      # 当前接触状态
        self._in_acceptance_ball = torch.zeros(self.num_envs, dtype=torch.bool, device=self.device)    # 是否在接受球内
        self._in_acceptance_xy = torch.zeros(self.num_envs, dtype=torch.bool, device=self.device)      # 是否在水平接受区域内

        # ========== 期望速度 ==========
        self._lin_vel_des = torch.zeros(3, device=self.device)
        self._lin_vel_des[0], self._lin_vel_des[1], self._lin_vel_des[2] = self.cfg.vx_des, self.cfg.vy_des, self.cfg.vz_des

        # ========== 动作空间初始化 ==========
        self._actions = torch.zeros(self.num_envs, gym.spaces.flatdim(self.single_action_space), device=self.device)          # 当前动作
        self._actions_filtered = torch.zeros_like(self._actions)    # 滤波后的动作
        self._previous_actions = torch.zeros_like(self._actions)    # 上一步动作

        # ========== 动作历史记录 ==========
        self._action_history = torch.zeros(self.num_envs, self.cfg.action_history_length, gym.spaces.flatdim(self.single_action_space), device=self.device)

        # ========== 推力和力矩 ==========
        # 应用到旋翼体的推力和力矩
        self.thrust = torch.zeros(self.num_envs, 4, 3, device=self.device)  # 4个旋翼的推力
        self.moment = torch.zeros(self.num_envs, 4, 3, device=self.device)  # 4个旋翼的力矩

        # ========== 关节速度和位置 ==========
        self._tilt_vel = torch.zeros(self.num_envs, 1, device=self.device)     # 倾斜速度
        self._tilt_angle = torch.zeros(self.num_envs, 1, device=self.device)   # 倾斜角度

        # ========== 目标位置 ==========
        self._desired_pos_w = torch.zeros(self.num_envs, 3, device=self.device)  # 世界坐标系中的目标位置

        # ========== 冲击力相关 ==========
        self._current_impulse = torch.zeros(self.num_envs, 1, device=self.device)  # 当前冲击力

        # ========== 加速度计算 ==========
        self._previous_lin_vel_w = torch.zeros(self.num_envs, 3, device=self.device)  # 上一步线速度
        self._acceleration       = torch.zeros(self.num_envs, 3, device=self.device)  # 加速度

        # ========== 首次接触标志 ==========
        # 所有环境开始时都为False
        self._first_contact = torch.zeros(self.num_envs, dtype=torch.bool, device=self.device)

        # ========== 干扰力和力矩 ==========
        self._disturbance_force  = torch.zeros(self.num_envs, 1, 3, device=self.device)  # 干扰力
        self._disturbance_moment = torch.zeros(self.num_envs, 1, 3, device=self.device)  # 干扰力矩

        # ========== 推力时间参数 ==========
        self._push_time = torch.zeros(self.num_envs, device=self.device)        # 推力开始时间
        self._push_duration = torch.zeros(self.num_envs, device=self.device)    # 推力持续时间

        # ========== 时间记录 ==========
        self._time_elapsed = torch.zeros(self.num_envs, device=self.device)     # 每个环境中的经过时间

        # ========== 滤波器系数 ==========
        self._alpha = self.cfg.alpha_0 * torch.ones(self.num_envs, 1, device=self.device)  # 低通滤波器系数

        # Logging
        self._episode_sums = {
            key: torch.zeros(self.num_envs, dtype=torch.float, device=self.device)
            for key in [
                "lin_vel_pen",
                "ang_vel_pen",
                "action_rate_pen",
                "ground_thrust_penalty",
                "spin_penalty",
                "orientation_pen",
                "died_penalty",
                "impulse_penalty",
                "distance_to_goal_xy_rew",
                "descending_rew",
                "tilt_rew",
                "contact_in_acceptance_rew",
            ]
        }

        # Get specific body indices
        self._base_link = self._robot.find_bodies("base_link")[0][0]
        self._rotor0    = self._robot.find_bodies("rotor0")[0][0]
        self._rotor1    = self._robot.find_bodies("rotor1")[0][0]
        self._rotor2    = self._robot.find_bodies("rotor2")[0][0]
        self._rotor3    = self._robot.find_bodies("rotor3")[0][0]

        # Get the joint indices
        self._joint0 = self._robot.find_joints("base_to_arml")[0][0]
        self._joint1 = self._robot.find_joints("base_to_armr")[0][0]

        # Get arml and armr indices
        self._arml = self._robot.find_bodies("arml")[0][0]
        self._armr = self._robot.find_bodies("armr")[0][0]

        # Get inertial parameters
        self._robot_mass = self._robot.root_physx_view.get_masses()[0].sum()
        self._gravity_magnitude = torch.tensor(self.sim.cfg.gravity, device=self.device).norm()
        self._robot_weight = (self._robot_mass * self._gravity_magnitude).item()

        # Initialize kT, kM and max_tilt_vel_0
        self.kT           = self.cfg.kT_0 * torch.ones(self.num_envs, 1, device=self.device)
        self.kM           = self.cfg.kM_0 * torch.ones(self.num_envs, 1, device=self.device)
        self.max_tilt_vel = self.cfg.max_tilt_vel_0 * torch.ones(self.num_envs, 1, device=self.device)

        # add handle for debug visualization (this is set to a valid handle inside set_debug_vis)
        self.set_debug_vis(self.cfg.debug_vis)

    def _setup_scene(self):
        self._robot = Articulation(self.cfg.robot)
        self.scene.articulations["robot"] = self._robot
        self._contact_sensor = ContactSensor(self.cfg.contact_sensor)
        self.scene.sensors["contact_sensor"] = self._contact_sensor

        self.cfg.terrain.num_envs = self.scene.cfg.num_envs
        self.cfg.terrain.env_spacing = self.scene.cfg.env_spacing
        self._terrain = self.cfg.terrain.class_type(self.cfg.terrain)
        
        # clone, filter, and replicate
        self.scene.clone_environments(copy_from_source=False)
        self.scene.filter_collisions(global_prim_paths=[self.cfg.terrain.prim_path])
        
        # add lights
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor):

        # increment elapsed time
        self._time_elapsed += self.step_dt

        # Clamp actions to [0, 1] even though sigmoid already achieves this
        self._actions = actions.clone().clamp(0.0, 1.0)

        # Pass thruster actions through a low pass filter
        if self.cfg.actuator_dynamics:
            self._actions_filtered[:, :4] = self._alpha * self._actions[:,:4] + (1 - self._alpha) * self._actions_filtered[:,:4]
            self._actions_filtered[:, 4]  = self._actions[:, 4]
        else:
            self._actions_filtered = self._actions

        if self.cfg.quantize_tilt_action:
            self._actions_filtered[:, 4] = torch.round(self._actions_filtered[:, 4])

        # Assign the thrust to each of the rotors
        spin_direction = torch.tensor([-1.0, -1.0, 1.0, 1.0], device=self.device)    
        self.thrust[:,:,2] = (self.kT.reshape(self.num_envs, 1, 1) * self._actions_filtered.reshape(self.num_envs, 1, 5)[:, :, :4]).squeeze()
        self.moment[:,:,2] = spin_direction * self.kM * self.thrust[:, :, 2]

        # Assign the joint positions and velocities
        tilt_action        = self._actions_filtered[:, 4].unsqueeze(1)
        self._tilt_angle   = self._tilt_angle + self.max_tilt_vel * tilt_action * self.physics_dt
        self._tilt_angle   = torch.clamp(self._tilt_angle,0.0,torch.pi/2)
        self._tilt_vel     = self.max_tilt_vel * tilt_action

    def _apply_action(self):

        dist_force      = torch.zeros(self.num_envs, 1, 3, device=self.device)
        dist_moment     = torch.zeros(self.num_envs, 1, 3, device=self.device)
        dist_force_cts  = torch.zeros(self.num_envs, 1, 3, device=self.device)
        dist_moment_cts = torch.zeros(self.num_envs, 1, 3, device=self.device)
        if self.cfg.disturb:
            # Determine whether to push robot
            push = torch.logical_and(self._time_elapsed >= self._push_time, self._time_elapsed <= self._push_time + self._push_duration).reshape(self.num_envs, 1, 1)
            
            # Compute disturbance force
            dist_force = self._disturbance_force * push
            dist_moment = self._disturbance_moment * push

            # Apply another disturbance force and moment of different nature
            dist_force_cts        = torch.zeros(self.num_envs, 1, 3, device=self.device).uniform_(-self.cfg.dist_force_cts_scale, self.cfg.dist_force_cts_scale)
            dist_moment_cts       = torch.zeros(self.num_envs, 1, 3, device=self.device).uniform_(-self.cfg.dist_moment_cts_scale, self.cfg.dist_moment_cts_scale)

        # Get total force and moment
        total_force  = torch.cat([self.thrust, dist_force + dist_force_cts], dim=1)
        total_moment = torch.cat([self.moment, dist_moment + dist_moment_cts], dim=1)

        # Apply forces, moments and joint targets
        self._robot.set_external_force_and_torque(total_force, total_moment, body_ids=[self._rotor0,self._rotor1,self._rotor2,self._rotor3,self._base_link])
        self._robot.set_joint_velocity_target(self._tilt_vel.repeat(1,2), joint_ids=[self._joint0,self._joint1])
        self._robot.set_joint_position_target(self._tilt_angle.repeat(1,2), joint_ids=[self._joint0,self._joint1])

    def _get_observations(self) -> dict:
        self._action_history     = torch.cat([self._actions.clone().unsqueeze(dim=1), self._action_history[:, :-1]], dim=1)
        relative_pos_w           = self._desired_pos_w - self._robot.data.root_link_pos_w
        tilt_angle               = self._robot.data.joint_pos[:, self._joint0].unsqueeze(dim=1)
        impulse                  = self.step_dt * torch.sum(torch.linalg.norm((self._contact_sensor.data.net_forces_w_history[:,1,:,:] - self._contact_sensor.data.net_forces_w_history[:,0,:,:]) * self.step_dt, dim=-1),dim=1).unsqueeze(dim=1) # type: ignore
        self._current_impulse    = impulse 
        rot                      = matrix_from_quat(self._robot.data.root_link_quat_w)
        rot_vector               = rot.reshape(-1, 9)

        noise = torch.cat(
            [
                self.cfg.pos_noise_scale * torch.zeros_like(relative_pos_w).uniform_(-1,1),
                self.cfg.rot_noise_scale * torch.zeros_like(rot_vector).uniform_(-1,1),      
                self.cfg.lin_vel_noise_scale * torch.zeros_like(self._robot.data.root_com_lin_vel_w).uniform_(-1,1),
                self.cfg.ang_vel_noise_scale * torch.zeros_like(self._robot.data.root_com_ang_vel_b).uniform_(-1,1),
                self.cfg.tilt_noise_scale * torch.zeros_like(tilt_angle).uniform_(-1,1),
                torch.reshape(torch.zeros_like(self._action_history), (self.num_envs, -1)),
            ],
            dim=-1,
        )
        obs = torch.cat(
            [
                relative_pos_w,      
                rot_vector,                     
                self._robot.data.root_com_lin_vel_w,                   
                self._robot.data.root_com_ang_vel_b,
                tilt_angle,
                torch.reshape(self._action_history, (self.num_envs, -1)),
            ],
            dim=-1,
        )
        obs_privileged = torch.cat(
            [
                self._disturbance_force[:,0,:],
                self._disturbance_moment[:,0,:],
                self._push_time.unsqueeze(dim=1),
                self._push_duration.unsqueeze(dim=1),
                self._time_elapsed.unsqueeze(dim=1),
                self._current_impulse,
                self._actions_filtered,
                self._alpha,
            ],
            dim=-1,
        )

        # get final observations
        obs_current = obs + self.cfg.noise * noise
        self._observation_buffer = torch.cat([obs_current.unsqueeze(1), self._observation_buffer[:, :-1]], dim=1)
        obs_policy = torch.reshape(self._observation_buffer[:,self.cfg.observation_delay:self.cfg.observation_delay+self.cfg.observation_history_length+1], (self.num_envs, -1))
        
        obs_critic = torch.cat([torch.reshape(self._observation_buffer, (self.num_envs, -1)), obs_privileged], dim=-1)

        observations = {"policy": obs_policy, "critic": obs_critic}
        return observations

    def _get_rewards(self) -> torch.Tensor:

        # determine if terminal state has been reached
        died, _ = self._get_dones()

        # contacts
        current_contact_time                 = self.scene["contact_sensor"].data.current_contact_time[:, [self._arml, self._armr]]
        num_contact                          = torch.sum(current_contact_time > 0.0, dim=1)
        self._current_contacts               = num_contact > 0
        new_contacts                         = torch.logical_and(torch.logical_xor(self._current_contacts , self._first_contact),self._current_contacts )
        new_contact_idx                      = torch.nonzero(new_contacts)
        self._first_contact[new_contact_idx] = new_contacts[new_contact_idx]

        # distance to goal
        distance_to_goal_xy     = torch.linalg.norm(self._desired_pos_w[:,:2] - self._robot.data.root_link_pos_w[:,:2], dim=1)
        distance_to_goal_xy_mapped = torch.exp(-distance_to_goal_xy / 0.25)

        # height
        height        = self._robot.data.root_link_pos_w[:, 2]
        height_mapped = torch.exp(-torch.square(height) / 0.25)

        # linear and angular velocity
        lin_vel        = torch.sum(torch.square(self._robot.data.root_com_lin_vel_w), dim=1)
        lin_vel_mapped = torch.exp(-lin_vel / 0.25)
        ang_vel        = torch.sum(torch.square(self._robot.data.root_com_ang_vel_b[:,:2]), dim=1)
        ang_vel_mapped = torch.exp(-ang_vel / 0.25)
        spin_vel = torch.square(self._robot.data.root_com_ang_vel_b[:,2])
        spin_vel_mapped = torch.exp(-spin_vel / 0.25)

        # reward descending z velocity
        descending_error        = torch.square(self._robot.data.root_com_lin_vel_w[:, 2] - self._lin_vel_des[2]) * ~self._first_contact
        descending_error_mapped = torch.exp(-descending_error / 0.25)

        # tilt error
        tilt              = self._robot.data.joint_pos[:, self._joint0]
        tilt_error        = torch.square(tilt - pi/2)
        tilt_error_mapped = torch.exp(-tilt_error / 0.25)

        # landing acceptance state
        self._in_acceptance_xy = (distance_to_goal_xy - self.cfg.delta_d) < 0.0

        # action rate
        action_rate      = torch.sum(torch.square(self._actions - self._action_history[:,0,:]), dim=1)
        # action_rate_tilt = torch.sum(torch.square(self._actions[:,-1].unsqueeze(1)  - self._action_history[:,:,-1]), dim=1)
        # action_rate_mapped = torch.exp(-action_rate/0.25)
        # action_rate_tilt_mapped = torch.exp(-action_rate_tilt/0.25)

        # reward low thruster actions that occur after first contact
        ground_thrust = torch.sum(torch.square(self._actions[:,:4]), dim=1) * self._first_contact
        ground_thrust_mapped = torch.exp(-ground_thrust / 0.25)

        # orientation
        flat_orientation = torch.abs(1 -  self.quat_axis(self._robot.data.root_link_quat_w, 2)[..., 2])
        flat_orientation_mapped = torch.exp(-flat_orientation / 0.25)

        rewards = {
            "lin_vel_pen":                    lin_vel                                            * self.cfg.lin_vel_pen_scale                  * self.step_dt, # policy obs: lin_vel (yes)
            "ang_vel_pen":                    ang_vel                                            * self.cfg.ang_vel_pen_scale                  * self.step_dt, # policy obs: ang_vel (yes)
            "action_rate_pen":                action_rate                                        * self.cfg.action_rate_pen_scale              * self.step_dt, # policy obs: previous action (yes)
            "ground_thrust_penalty":          ground_thrust                                      * self.cfg.ground_thrust_pen_scale            * self.step_dt, # critic obs: current_contacts (yes)
            "spin_penalty":                   spin_vel                                           * self.cfg.spin_pen_scale                     * self.step_dt, # critic obs: omegaz
            "orientation_pen":                flat_orientation                                   * self.cfg.orientation_pen_scale              * self.step_dt, # policy obs: root_link_quat_w (yes)
 
            "died_penalty":                   died                                               * self.cfg.died_pen,                                          # no observation
            "impulse_penalty":                self._current_impulse.squeeze(dim=1)               * self.cfg.impulse_pen,                                       # critic obs: current_impulse (yes)
             
            "distance_to_goal_xy_rew":        distance_to_goal_xy_mapped                         * self.cfg.distance_to_goal_xy_rew_scale      * self.step_dt, # policy obs: relative_pos (yes)
            "descending_rew":                 descending_error_mapped                            * self.cfg.descending_rew_scale               * self.step_dt, # no observation
            "tilt_rew":                       tilt_error_mapped * height_mapped                  * self.cfg.tilt_rew_scale                     * self.step_dt, # policy obs: tilt_angle (yes)
            "contact_in_acceptance_rew":      self._current_contacts  * self._in_acceptance_xy   * self.cfg.contact_in_acceptance_rew_scale    * self.step_dt, # critic obs: in_acceptance_ball (yes)

        }
        reward = torch.sum(torch.stack(list(rewards.values())), dim=0)
        
        # Logging
        for key, value in rewards.items():
            self._episode_sums[key] += value
        return reward
    
    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        time_out = self.episode_length_buf >= self.max_episode_length - 1                                                                              
        died1 = torch.linalg.norm(self._robot.data.root_com_lin_vel_w, dim=1) > self.cfg.too_fast_vel
        died2 =  torch.linalg.norm(self._desired_pos_w[:,:2] - self._robot.data.root_link_pos_w[:,:2], dim=1) > self.cfg.termination_dxy
        died3 =  self._robot.data.root_link_pos_w[:, 2] > self.cfg.termination_height
        if self.cfg.terminate: died = died1 | died2 | died3
        else: died = torch.zeros(self.num_envs, device=self.device)
        return died, time_out

    def _reset_idx(self, env_ids: torch.Tensor | None):
        if env_ids is None or len(env_ids) == self.num_envs:
            env_ids = self._robot._ALL_INDICES

        # Logging
        final_distance_to_goal = torch.linalg.norm(
            self._desired_pos_w[env_ids] - self._robot.data.root_link_pos_w[env_ids], dim=1
        ).mean()
        extras = dict()
        for key in self._episode_sums.keys():
            episodic_sum_avg = torch.mean(self._episode_sums[key][env_ids])
            extras["Episode_Reward/" + key] = episodic_sum_avg / self.max_episode_length_s
            self._episode_sums[key][env_ids] = 0.0
        self.extras["log"] = dict()
        self.extras["log"].update(extras)
        extras = dict()
        extras["Episode_Termination/died"] = torch.count_nonzero(self.reset_terminated[env_ids]).item()
        extras["Episode_Termination/time_out"] = torch.count_nonzero(self.reset_time_outs[env_ids]).item()
        extras["Metrics/final_distance_to_goal"] = final_distance_to_goal.item()
        extras["Metrics/distance_to_goal_epoch_av"] = self.distance_to_goal_epoch_av
        self.extras["log"].update(extras)

        self._robot.reset(env_ids)
        super()._reset_idx(env_ids)
        if len(env_ids) == self.num_envs:
            # Spread out the resets to avoid spikes in training when many environments reset at a similar time
            self.episode_length_buf = torch.randint_like(self.episode_length_buf, high=int(self.max_episode_length))

        if self.cfg.randomize:
            self._observation_buffer[env_ids] = torch.zeros_like(self._observation_buffer[env_ids])

            self._actions[env_ids] = torch.ones_like(self._actions[env_ids])
            self._actions_filtered[env_ids] = torch.zeros_like(self._actions_filtered[env_ids])
            self._action_history[env_ids] = torch.zeros_like(self._action_history[env_ids])

            # Sample new desired positions
            self._desired_pos_w[env_ids, :2] = torch.zeros_like(self._desired_pos_w[env_ids, :2]).uniform_(-self.box_extent, self.box_extent)
            self._desired_pos_w[env_ids, :2] += self._terrain.env_origins[env_ids, :2]
            self._desired_pos_w[env_ids, 2] =  torch.zeros_like(self._desired_pos_w[env_ids, 2])
            
            # Get the default root state of the robot
            default_root_state = self._robot.data.default_root_state[env_ids]

            # Make sure the root positions are adjusted by the terrain origins
            default_root_state[:, :3] += self._terrain.env_origins[env_ids]

            # Randomize the initial height
            default_root_state[:, 2] = torch.zeros_like(default_root_state[:, 2]).uniform_(self.cfg.initial_height_range[0], self.cfg.initial_height_range[1])

            # Randomize the initial linear velocity 
            default_root_state[:, 7:10] = torch.zeros_like(default_root_state[:, 7:10]).uniform_(self.cfg.initial_lin_vel_range[0], self.cfg.initial_lin_vel_range[1])

            # Randomize the initial angular velocity
            default_root_state[:, 10:13] = torch.zeros_like(default_root_state[:, 10:13]).uniform_(self.cfg.initial_ang_vel_range[0], self.cfg.initial_ang_vel_range[1])

            # Randomize the initial orientation by sampling a random quaternion
            default_root_state[:, 3:7], _ = self.random_quaternion(len(env_ids))

            # Randomize the initial tilt angle 
            joint_pos = self._robot.data.default_joint_pos[env_ids]
            random_tilt = torch.zeros_like(joint_pos[:, self._joint0]).uniform_(self.cfg.initial_tilt_range[0], self.cfg.initial_tilt_range[1])
            joint_pos[:, self._joint0] = random_tilt.clone()
            joint_pos[:, self._joint1] = random_tilt.clone()

            # Also set the initial tilt angle
            self._tilt_angle[env_ids,0] = random_tilt.clone()
            
            # Joint velocities randomly initialized
            joint_vel = self._robot.data.default_joint_vel[env_ids]
            random_vel = torch.zeros_like(joint_vel[:, self._joint0]).uniform_(self.cfg.initial_tilt_vel_range[0], self.cfg.initial_tilt_vel_range[1])
            joint_vel[:, self._joint0] = random_vel.clone()
            joint_vel[:, self._joint1] = random_vel.clone()

            # Write the root state to the simulation
            self._robot.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)
            self._robot.write_root_com_velocity_to_sim(default_root_state[:, 7:], env_ids)
            self._robot.write_root_link_pose_to_sim(default_root_state[:, :7], env_ids)

            # Reset the first contact array 
            self._first_contact[env_ids] = False

            # Randomize kT, kM and max_tilt_vel
            self.kT[env_ids]           = self.cfg.kT_0 * ( 1 + self.cfg.kT_error_scale * torch.zeros_like(self.kT[env_ids]).uniform_(-1., 1.))
            self.kM[env_ids]           = self.cfg.kM_0 * ( 1 + self.cfg.kM_error_scale * torch.zeros_like(self.kM[env_ids]).uniform_(-1., 1.))
            self.max_tilt_vel[env_ids] = self.cfg.max_tilt_vel_0 * ( 1 + self.cfg.max_tilt_vel_error_scale * torch.zeros_like(self.max_tilt_vel[env_ids]).uniform_(-1., 1.))

            # Sample disturbance directions for the episode
            disturbance_force_direction  = torch.normal(0.0, 1.0, size=(self.num_envs,1,3),device=self.device)
            disturbance_force_direction  = disturbance_force_direction / (torch.linalg.norm(disturbance_force_direction, dim=1).unsqueeze(dim=1) + 1e-6)
            
            disturbance_moment_direction  = torch.normal(0.0, 1.0, size=(self.num_envs,1,3),device=self.device)
            disturbance_moment_direction  = disturbance_moment_direction / (torch.linalg.norm(disturbance_moment_direction, dim=1).unsqueeze(dim=1) + 1e-6)

            # Randomize the push time and push duration
            self._push_time[env_ids]     = self.cfg.episode_length_s * torch.zeros_like(self._push_time[env_ids]).uniform_(0.0, 0.5)  # best 0.8
            self._push_duration[env_ids] = torch.zeros_like(self._push_duration[env_ids]).uniform_(0.0, 0.2) # best 1.0

            # Randomize disturbance intensity 
            force_intensity = torch.normal(torch.tensor(0.0), self.cfg.disturbance_force_scale)
            moment_intensity = torch.normal(torch.tensor(0.0), self.cfg.disturbance_moment_scale)
            self._disturbance_force  = force_intensity * disturbance_force_direction
            self._disturbance_moment = moment_intensity * disturbance_moment_direction

            # Randomize self._alpha
            if self.cfg.randomize_motor_dynamics:
                self._alpha = torch.zeros_like(self._alpha).uniform_(self.cfg.alpha_range[0], self.cfg.alpha_range[1])

            # Reset time
            self._time_elapsed[env_ids] = 0.0

            # Reset impulse
            self._current_impulse[env_ids] = 0.0

            # Reset current contacts and in acceptance ball
            self._current_contacts = torch.zeros_like(self._current_contacts)
            self._in_acceptance_ball = torch.zeros_like(self._in_acceptance_ball)
            self._in_acceptance_xy = torch.zeros_like(self._in_acceptance_xy)

        else:
            self._observation_buffer[env_ids] = torch.zeros_like(self._observation_buffer[env_ids])

            self._actions[env_ids] = torch.zeros_like(self._actions[env_ids])
            self._actions_filtered[env_ids] = torch.zeros_like(self._actions_filtered[env_ids])
            self._action_history[env_ids] = torch.zeros_like(self._action_history[env_ids])

            self._desired_pos_w[env_ids, :2] = torch.zeros_like(self._desired_pos_w[env_ids, :2]).uniform_(-0.1,0.1)
            self._desired_pos_w[env_ids, :2] += self._terrain.env_origins[env_ids, :2]
            self._desired_pos_w[env_ids, 2]  =  torch.zeros_like(self._desired_pos_w[env_ids, 2])

            default_root_state = self._robot.data.default_root_state[env_ids]
            default_root_state[:, :3] += self._terrain.env_origins[env_ids]

            joint_pos = self._robot.data.default_joint_pos[env_ids]
            self._tilt_angle[env_ids,0] = 0.0

            joint_vel = self._robot.data.default_joint_vel[env_ids]

            self._robot.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)
            self._robot.write_root_com_velocity_to_sim(default_root_state[:, 7:], env_ids)
            self._robot.write_root_link_pose_to_sim(default_root_state[:, :7], env_ids)

            # Choose disturbance directions to test robot
            # disturbance_force_direction  = torch.normal(0.0, 1.0, size=(1,1,3),device=self.device).repeat(self.num_envs,1,1)
            disturbance_force_direction  = torch.tensor([1.0,0.0,0.0],device=self.device).repeat(self.num_envs, 1, 1)
            disturbance_force_direction  = disturbance_force_direction / (torch.linalg.norm(disturbance_force_direction, dim=1).unsqueeze(dim=1) + 1e-6)
            
            disturbance_moment_direction  = torch.normal(0.0, 1.0, size=(1,1,3),device=self.device).repeat(self.num_envs,1,1)      
            disturbance_moment_direction  = disturbance_moment_direction / (torch.linalg.norm(disturbance_moment_direction, dim=1).unsqueeze(dim=1) + 1e-6)

            # Randomize the push time and push duration
            self._push_time[env_ids]     = 0.5 * torch.ones_like(self._push_time[env_ids])
            self._push_duration[env_ids] = 0.5 * torch.ones_like(self._push_duration[env_ids])

            # Randomize disturbance intensity 
            # force_intensity = torch.normal(torch.tensor(0.0), self.cfg.disturbance_force_scale)
            force_intensity = 4 * self.cfg.kT_0 * 0.15 
            moment_intensity = torch.normal(torch.tensor(0.0), self.cfg.disturbance_moment_scale)
            self._disturbance_force  = force_intensity * disturbance_force_direction
            self._disturbance_moment = moment_intensity * disturbance_moment_direction
            
            self._first_contact[env_ids]   = False
            self._time_elapsed[env_ids]    = 0.0
            self._current_impulse[env_ids] = 0.0

            # Reset current contacts and in acceptance ball
            self._current_contacts = torch.zeros_like(self._current_contacts)
            self._in_acceptance_ball = torch.zeros_like(self._in_acceptance_ball)
            self._in_acceptance_xy = torch.zeros_like(self._in_acceptance_xy)

    def _set_debug_vis_impl(self, debug_vis: bool):
        # create markers if necessary for the first time
        if debug_vis:
            if not hasattr(self, "goal_pos_visualizer"):
                marker_cfg = CUBOID_MARKER_CFG.copy()
                marker_cfg.markers["cuboid"].size = (0.05, 0.05, 0.05)
                # -- goal pose
                marker_cfg.prim_path = "/Visuals/Command/goal_position"
                self.goal_pos_visualizer = VisualizationMarkers(marker_cfg)
            # set their visibility to true
            self.goal_pos_visualizer.set_visibility(True)
        else:
            if hasattr(self, "goal_pos_visualizer"):
                self.goal_pos_visualizer.set_visibility(False)

    def _debug_vis_callback(self, event):
        # update the markers
        self.goal_pos_visualizer.visualize(self._desired_pos_w)

    def random_quaternion(self, num):
        """Returns sampled rotation around z-axis.

        Args:
            num: The number of rotations to sample.

        Returns:
            Sampled quaternion in (w, x, y, z). Shape is (num, 4).
        """
        roll = torch.pi / 6 * (2 * torch.rand(num, dtype=torch.float) - 1)
        pitch = torch.pi / 6 * (2 * torch.rand(num, dtype=torch.float) - 1)
        yaw = 2 * torch.pi * torch.rand(num, dtype=torch.float)

        return quat_from_euler_xyz(roll, pitch, yaw), yaw.to(self.device)
    
    def quat_axis(self, q, axis=0):
        # type: (Tensor, int) -> Tensor
        basis_vec = torch.zeros(q.shape[0], 3, device=q.device)
        basis_vec[:, axis] = 1
        return quat_rotate(q, basis_vec)

    def tensor_clamp(self, t, min_t, max_t):
        return torch.max(torch.min(t, max_t), min_t)