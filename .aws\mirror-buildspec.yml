version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 14
  pre_build:
    commands:
      - git config --global user.name "Isaac LAB CI Bot"
      - git config --global user.email "<EMAIL>"
  build:
    commands:
      - git remote set-url origin https://github.com/${TARGET_REPO}.git
      - git checkout $SOURCE_BRANCH
      - git push --force https://$<EMAIL>/${TARGET_REPO}.git $SOURCE_BRANCH:$TARGET_BRANCH
