---
name: Bug Report
about: Submit a bug report
title: "[Bug Report] Bug title"

---

If you are submitting a bug report, please fill in the following details and use the tag [bug].

### Describe the bug

A clear and concise description of what the bug is.

### Steps to reproduce

Please try to provide a minimal example to reproduce the bug. Error messages and stack traces are also helpful.

<!-- Please post terminal logs, minimal example to reproduce, or command to run under three backticks (```) to allow code formatting.

```
Paste your error here
```

For more information on this, check: https://www.markdownguide.org/extended-syntax/#fenced-code-blocks

-->

### System Info

Describe the characteristic of your environment:

<!-- Please complete the following description. -->
- Commit: [e.g. 8f3b9ca]
- <PERSON>m Version: [e.g. 2022.2.0, this can be obtained by `cat ${ISAACSIM_PATH}/VERSION`]
- OS: [e.g. Ubuntu 20.04]
- GPU: [e.g. RTX 2060 Super]
- CUDA: [e.g. 11.4]
- GPU Driver: [e.g. 470.82.01, this can be seen by using `nvidia-smi` command.]

### Additional context

Add any other context about the problem here.

### Checklist

- [ ] I have checked that there is no similar issue in the repo (**required**)
- [ ] I have checked that the issue is not in running <PERSON> Sim itself and is related to the repo

### Acceptance Criteria

Add the criteria for which this task is considered **done**. If not known at issue creation time, you can add this once the issue is assigned.

- [ ] Criteria 1
- [ ] Criteria 2
