{% if versions %}
<nav class="bd-links bd-docs-nav">
    <div class="bd-toc-item navbar-nav">
      <ul class="nav bd-sidenav">
        <li class="toctree-l1 has-children" style="display: flex; justify-content: center; align-items: center; flex-direction: column;">
          <div  style ="text-align:center;">
            <label for="version-select" style="font-weight: bold; display: block;">Version</label>
          </div>
          <select id="version-select" class="version-dropdown" style="margin: 0 auto; display: block;" onchange="location = this.value;">
            {%- for item in versions.branches %}
            <option value="{{ item.url }}" {% if item == current_version %}selected{% endif %}>{{ item.name }}</option>
            {%- endfor %}
            {%- for item in versions.tags|reverse %}
            <option value="{{ item.url }}" {% if item == current_version %}selected{% endif %}>{{ item.name }}</option>
            {%- endfor %}
          </select>
        </li>
      </ul>
    </div>
</nav>
{% endif %}
