# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
加州理工学院ATMO机器人配置文件

此文件定义了ATMO（Aerial Tilting Multirotor Omnicopter）机器人的完整配置，
包括物理属性、执行器设置和初始状态等。ATMO是一种具有可倾斜旋翼的多旋翼飞行器，
能够实现精确的空中操控和着陆。
"""

from __future__ import annotations

import omni.isaac.lab.sim as sim_utils                    # 仿真工具模块
from omni.isaac.lab.actuators import ImplicitActuatorCfg  # 隐式执行器配置
from omni.isaac.lab.assets import ArticulationCfg        # 关节机器人配置

##
# 配置参数
##

# ATMO机器人USD文件路径
# USD (Universal Scene Description) 是Pixar开发的3D场景描述格式
USD_PATH = "/home/<USER>/src/atmo_urdf/atmo/atmo.usd"

# ATMO机器人完整配置
ATMO_CFG = ArticulationCfg(
    # 生成配置 - 定义如何在仿真中创建机器人
    spawn=sim_utils.UsdFileCfg(
        usd_path=USD_PATH,                    # USD文件路径
        activate_contact_sensors=True,        # 激活接触传感器，用于检测碰撞
        # 刚体物理属性配置
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,                # 启用重力影响
            max_depenetration_velocity=10.0,      # 最大去穿透速度，防止物体相互穿透
            enable_gyroscopic_forces=True,        # 启用陀螺力效应，重要的飞行器物理特性
        ),
        # 关节根部属性配置
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,           # 禁用自碰撞检测
            solver_position_iteration_count=4,       # 位置求解器迭代次数，影响精度和稳定性
            solver_velocity_iteration_count=0,       # 速度求解器迭代次数
            sleep_threshold=0.005,                   # 休眠阈值，低于此速度时物体进入休眠状态
            stabilization_threshold=0.001,           # 稳定化阈值，用于数值稳定性
        ),
    ),
    # 初始状态配置
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 2.0),    # 初始位置：在地面上方2米处
        joint_pos={
            ".*": 0.0,          # 所有关节的初始位置设为0（使用正则表达式匹配所有关节）
        },
    ),
    # 执行器配置 - 定义机器人的驱动系统
    actuators={
        # 左臂执行器配置
        "arml_actuator": ImplicitActuatorCfg(
            joint_names_expr=["base_to_arml"],    # 控制的关节名称（左臂关节）
            effort_limit=1e16,                   # 力矩限制（设置为极大值，实际无限制）
            velocity_limit=30.0,                 # 速度限制 (rad/s)
            stiffness=1e15,                      # 刚度系数，控制位置跟踪精度
            damping=1e5,                         # 阻尼系数，控制振荡抑制
        ),
        # 右臂执行器配置
        "armr_actuator": ImplicitActuatorCfg(
            joint_names_expr=["base_to_armr"],    # 控制的关节名称（右臂关节）
            effort_limit=1e16,                   # 力矩限制（设置为极大值，实际无限制）
            velocity_limit=30,                   # 速度限制 (rad/s)
            stiffness=1e15,                      # 刚度系数，控制位置跟踪精度
            damping=1e5,                         # 阻尼系数，控制振荡抑制
        ),
    },
)

"""
ATMO机器人配置说明：

ATMO是一种创新的多旋翼飞行器设计，具有以下特点：
1. 可倾斜旋翼：通过arml和armr两个关节控制旋翼倾斜角度
2. 精确控制：高刚度和适当阻尼确保精确的位置和速度控制
3. 物理真实性：启用陀螺力效应和重力，提供真实的飞行动力学
4. 接触检测：激活接触传感器用于着陆检测和碰撞避免

执行器参数说明：
- effort_limit: 设置为极大值，表示不限制关节力矩
- velocity_limit: 限制关节最大角速度为30 rad/s
- stiffness: 高刚度值确保精确的位置跟踪
- damping: 适当的阻尼抑制振荡，提高稳定性
"""