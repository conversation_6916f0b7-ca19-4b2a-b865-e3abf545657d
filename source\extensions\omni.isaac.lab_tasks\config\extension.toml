[package]

# Note: Semantic Versioning is used: https://semver.org/
version = "0.10.18"

# Description
title = "Isaac Lab Environments"
description="Extension containing suite of environments for robot learning."
readme  = "docs/README.md"
repository = "https://github.com/isaac-sim/IsaacLab"
category = "robotics"
keywords = ["robotics", "rl", "il", "learning"]

[dependencies]
"omni.isaac.lab" = {}
"omni.isaac.lab_assets" = {}

[python.pipapi]
requirements = [
    "h5py",
    "tensorboard",
    "stable-baselines3>=2.1",
    "rl-games==1.6.1",
    "rsl-rl@git+https://github.com/leggedrobotics/rsl_rl.git",
    "skrl>=1.3.0"
]

modules = [
    "h5py",
    "tensorboard",
    "stable_baselines3",
    "rl_games",
    "rsl_rl",
    "skrl"
]

use_online_index=true

[[python.module]]
name = "omni.isaac.lab_tasks"
